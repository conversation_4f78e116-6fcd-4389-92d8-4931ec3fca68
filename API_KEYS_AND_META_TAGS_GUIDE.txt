===============================================================================
                    دليل تغيير API Keys و Meta Tags
                        SocialBoost Project Guide
===============================================================================

📋 المحتويات:
1. أماكن تغيير API Keys
2. شرح Meta Tags
3. معلومات إضافية مهمة

===============================================================================
🔑 1. أماكن تغيير API Keys
===============================================================================

📱 Instagram (Instagram/Instagram-script.js):
----------------------------------------------
الملف: Instagram/Instagram-script.js
السطر: 286
الموقع: داخل دالة fetchInstagramProfile

البحث عن:
'x-rapidapi-key': '**************************************************'

استبدال بـ:
'x-rapidapi-key': 'YOUR_RAPIDAPI_KEY_HERE'

API المستخدم:
- الاسم: Instagram Scrapper Posts Reels Stories Downloader
- الرابط: instagram-scrapper-posts-reels-stories-downloader.p.rapidapi.com
- الوظيفة: جلب بيانات البروفايل من Instagram

----------------------------------------------

🎵 TikTok (TikTok/TikTok-script.js):
----------------------------------------------
الملف: TikTok/TikTok-script.js
السطر: 281
الموقع: داخل دالة fetchTikTokProfile

البحث عن:
'x-rapidapi-key': '**************************************************'

استبدال بـ:
'x-rapidapi-key': 'YOUR_RAPIDAPI_KEY_HERE'

API المستخدم:
- الاسم: TikTok API6
- الرابط: tiktok-api6.p.rapidapi.com
- الوظيفة: جلب بيانات البروفايل من TikTok

----------------------------------------------

👻 Snapchat (Snapchat/Snapchat-script.js):
----------------------------------------------
ملاحظة مهمة: Snapchat لا يستخدم API حقيقي!

الملف: Snapchat/Snapchat-script.js
السطر: 280 (معطل)
الحالة: تم تعطيل API واستخدام بيانات وهمية محلية

السبب: تم تعديل الكود ليعمل بدون API ويستخدم فقط الاسم المدخل
مع صورة بروفايل بالحرف الأول وبيانات ثابتة (0 متابعين، 0 نقاط).

إذا كنت تريد تفعيل API مرة أخرى:
البحث عن: fetchSnapchatProfile (الدالة معطلة حالياً)
API المطلوب: Snapchat Profile Scraper API

===============================================================================
🏷️ 2. شرح Meta Tags (index.html)
===============================================================================

📍 موقع Meta Tags:
الملف: index.html
السطور: 7-65

🔍 أنواع Meta Tags المستخدمة:

----------------------------------------------
📌 Primary Meta Tags (الأساسية):
----------------------------------------------
<meta name="title" content="SocialBoost - Get 100% Free Followers for All Platforms">
<meta name="description" content="Boost your social media presence across Instagram, TikTok, Snapchat and more! Get real, active followers completely free with our trusted service.">
<meta name="keywords" content="free followers, instagram followers, tiktok followers, snapchat followers, social media boost, free likes, social media growth">
<meta name="author" content="SocialBoost">
<meta name="robots" content="index, follow">

الغرض: تحسين محركات البحث (SEO) الأساسي

----------------------------------------------
📘 Open Graph Meta Tags (فيسبوك/لينكد إن):
----------------------------------------------
<meta property="og:type" content="website">
<meta property="og:url" content="https://socialboost.com/">
<meta property="og:title" content="SocialBoost - Get 100% Free Followers for All Platforms">
<meta property="og:description" content="Boost your social media presence across Instagram, TikTok, Snapchat and more! Get real, active followers completely free with our trusted service.">
<meta property="og:image" content="https://socialboost.com/images/og-image.jpg">
<meta property="og:site_name" content="SocialBoost">

الغرض: تحسين مظهر الموقع عند المشاركة على فيسبوك ولينكد إن

----------------------------------------------
🐦 Twitter Card Meta Tags:
----------------------------------------------
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="https://socialboost.com/">
<meta property="twitter:title" content="SocialBoost - Get 100% Free Followers for All Platforms">
<meta property="twitter:description" content="Boost your social media presence across Instagram, TikTok, Snapchat and more! Get real, active followers completely free with our trusted service.">
<meta property="twitter:image" content="https://socialboost.com/images/twitter-image.jpg">

الغرض: تحسين مظهر الموقع عند المشاركة على تويتر

----------------------------------------------
📱 Mobile & App Meta Tags:
----------------------------------------------
<meta name="theme-color" content="#667eea">
<meta name="msapplication-TileColor" content="#667eea">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="SocialBoost">

الغرض: تحسين تجربة المستخدم على الهواتف المحمولة

----------------------------------------------
🔗 Additional Meta Tags:
----------------------------------------------
<link rel="canonical" href="https://socialboost.com/">
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

الغرض: تحديد الرابط الأساسي وأيقونات الموقع

----------------------------------------------
📊 Structured Data (JSON-LD):
----------------------------------------------
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "SocialBoost",
    "url": "https://socialboost.com",
    "description": "Get 100% free followers for Instagram, TikTok, Snapchat and more social media platforms",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "https://socialboost.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
    }
}
</script>

الغرض: مساعدة محركات البحث في فهم محتوى الموقع بشكل أفضل

===============================================================================
🔧 3. معلومات إضافية مهمة
===============================================================================

🚨 تحذيرات مهمة:
----------------------------------------------
1. لا تشارك API Keys في الكود المنشور علناً
2. استخدم متغيرات البيئة (Environment Variables) في الإنتاج
3. قم بتجديد API Keys بانتظام لضمان الأمان

📝 نصائح للتخصيص:
----------------------------------------------
1. غيّر الروابط في Meta Tags لتتناسب مع نطاقك
2. أضف صور مخصصة للـ og:image و twitter:image
3. عدّل الألوان في theme-color حسب تصميمك
4. حدّث معلومات الاتصال والوصف حسب مشروعك

🔄 كيفية الحصول على API Keys:
----------------------------------------------
1. اذهب إلى RapidAPI.com
2. ابحث عن APIs المطلوبة:
   - Instagram Scrapper Posts Reels Stories Downloader
   - TikTok API6
3. اشترك في الخطة المناسبة
4. انسخ API Key من لوحة التحكم
5. استبدل القيم في الملفات المذكورة أعلاه

📊 اختبار Meta Tags:
----------------------------------------------
يمكنك اختبار Meta Tags باستخدام:
1. Facebook Sharing Debugger
2. Twitter Card Validator  
3. Google Rich Results Test
4. LinkedIn Post Inspector

===============================================================================
📞 الدعم والمساعدة
===============================================================================

إذا واجهت أي مشاكل:
1. تأكد من صحة API Keys
2. تحقق من حدود الاستخدام للـ APIs
3. راجع وثائق APIs على RapidAPI
4. تأكد من تحديث الروابط في Meta Tags

تم إنشاء هذا الدليل في: 2025-01-18
إصدار المشروع: SocialBoost v1.0

===============================================================================
