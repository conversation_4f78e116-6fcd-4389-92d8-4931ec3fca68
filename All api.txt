instagram
const data = null;

const xhr = new XMLHttpRequest();
xhr.withCredentials = true;

xhr.addEventListener('readystatechange', function () {
	if (this.readyState === this.DONE) {
		console.log(this.responseText);
	}
});

xhr.open('GET', 'https://instagram-scrapper-posts-reels-stories-downloader.p.rapidapi.com/profile_by_username?username=noesis_ar');
xhr.setRequestHeader('x-rapidapi-key', '**************************************************');
xhr.setRequestHeader('x-rapidapi-host', 'instagram-scrapper-posts-reels-stories-downloader.p.rapidapi.com');

xhr.send(data);


tiktok
const data = null;

const xhr = new XMLHttpRequest();
xhr.withCredentials = true;

xhr.addEventListener('readystatechange', function () {
	if (this.readyState === this.DONE) {
		console.log(this.responseText);
	}
});

xhr.open('GET', 'https://tiktok-api6.p.rapidapi.com/user/details?username=mrbeast');
xhr.setRequestHeader('x-rapidapi-key', '**************************************************');
xhr.setRequestHeader('x-rapidapi-host', 'tiktok-api6.p.rapidapi.com');

xhr.send(data);


snapchat
const data = null;

const xhr = new XMLHttpRequest();
xhr.withCredentials = true;

xhr.addEventListener('readystatechange', function () {
	if (this.readyState === this.DONE) {
		console.log(this.responseText);
	}
});

xhr.open('GET', 'https://snapchat-profile-scraper-api.p.rapidapi.com/profile?username=loganpaul');
xhr.setRequestHeader('x-rapidapi-key', '**************************************************');
xhr.setRequestHeader('x-rapidapi-host', 'snapchat-profile-scraper-api.p.rapidapi.com');

xhr.send(data);


