// Comments functionality
let commentsData = [];
let displayedComments = 0;
const commentsPerLoad = 5;

async function loadComments() {
    try {
        const response = await fetch('comments.json');
        const data = await response.json();
        commentsData = data.comments;
        displayComments();
        startCommentsAnimation();
    } catch (error) {
        console.error('Error loading comments:', error);
    }
}

function displayComments() {
    const commentsList = document.getElementById('commentsList');
    if (!commentsList) return;
    
    const commentsToShow = commentsData.slice(displayedComments, displayedComments + commentsPerLoad);
    
    commentsToShow.forEach((comment, index) => {
        setTimeout(() => {
            const commentElement = createCommentElement(comment);
            commentsList.appendChild(commentElement);
        }, index * 200);
    });
    
    displayedComments += commentsToShow.length;
}

function createCommentElement(comment) {
    const commentDiv = document.createElement('div');
    commentDiv.className = 'comment-item';
    
    commentDiv.innerHTML = `
        <img src="${comment.avatar}" alt="${comment.username}" class="comment-avatar">
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-username">${comment.username}</span>
                ${comment.verified ? '<i class="fas fa-check-circle verified-badge"></i>' : ''}
                <span class="comment-time">${comment.time}</span>
            </div>
            <div class="comment-text">${comment.text}</div>
        </div>
    `;
    
    return commentDiv;
}

function startCommentsAnimation() {
    setInterval(() => {
        const commentsList = document.getElementById('commentsList');
        if (commentsList && commentsList.children.length > 10) {
            commentsList.removeChild(commentsList.firstChild);
        }
        
        const randomComment = commentsData[Math.floor(Math.random() * commentsData.length)];
        const commentElement = createCommentElement(randomComment);
        if (commentsList) {
            commentsList.appendChild(commentElement);
        }
    }, 3000);
}

// DOM Elements
let usernameInput, searchBtn, loadingContainer, errorContainer, errorMessage, retryBtn;
let step1, step2, step3;
let profileDisplay, profileImage, profileFullName, profileUsername, profileBio;
let currentFollowers, currentFollowing, currentPosts, verifiedBadge;
let startBoostingBtn;
let liveProfileImage, liveProfileFullName, liveProfileUsername, liveProfileBio, liveVerifiedBadge;
let livePosts, liveFollowerCount, liveFollowing, addedFollowers, targetFollowers;
let progressFill, progressPercentage, statusMessage;

// Global variables
let currentProfile = null;
let originalFollowerCount = 0;
let boostingInterval = null;
let selectedFollowerAmount = 0;

// Initialize DOM elements and event listeners
document.addEventListener('DOMContentLoaded', function() {
    loadComments();
    
    // Get DOM elements
    usernameInput = document.getElementById('usernameInput');
    searchBtn = document.getElementById('searchBtn');
    loadingContainer = document.getElementById('loadingContainer');
    errorContainer = document.getElementById('errorContainer');
    errorMessage = document.getElementById('errorMessage');
    retryBtn = document.getElementById('retryBtn');
    
    step1 = document.getElementById('step1');
    step2 = document.getElementById('step2');
    step3 = document.getElementById('step3');
    
    profileDisplay = document.getElementById('profileDisplay');
    profileImage = document.getElementById('profileImage');
    profileFullName = document.getElementById('profileFullName');
    profileUsername = document.getElementById('profileUsername');
    profileBio = document.getElementById('profileBio');
    currentFollowers = document.getElementById('currentFollowers');
    currentFollowing = document.getElementById('currentFollowing');
    currentPosts = document.getElementById('currentPosts');
    verifiedBadge = document.getElementById('verifiedBadge');
    
    startBoostingBtn = document.getElementById('startBoostingBtn');

    // Live profile elements
    liveProfileImage = document.getElementById('liveProfileImage');
    liveProfileFullName = document.getElementById('liveProfileFullName');
    liveProfileUsername = document.getElementById('liveProfileUsername');
    liveProfileBio = document.getElementById('liveProfileBio');
    liveVerifiedBadge = document.getElementById('liveVerifiedBadge');
    livePosts = document.getElementById('livePosts');
    liveFollowerCount = document.getElementById('liveFollowerCount');
    liveFollowing = document.getElementById('liveFollowing');
    addedFollowers = document.getElementById('addedFollowers');
    targetFollowers = document.getElementById('targetFollowers');
    progressFill = document.getElementById('progressFill');
    progressPercentage = document.getElementById('progressPercentage');
    statusMessage = document.getElementById('statusMessage');

    // Event Listeners
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }
    
    if (retryBtn) {
        retryBtn.addEventListener('click', () => {
            hideError();
            if (usernameInput) usernameInput.focus();
        });
    }

    if (usernameInput) {
        usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
        usernameInput.focus();
    }

    if (startBoostingBtn) {
        startBoostingBtn.addEventListener('click', startBoosting);
    }

    // Follower option selection
    const optionCards = document.querySelectorAll('.option-card');
    optionCards.forEach(card => {
        card.addEventListener('click', () => {
            // Remove selected class from all cards
            optionCards.forEach(c => c.classList.remove('selected'));
            // Add selected class to clicked card
            card.classList.add('selected');
            // Get selected amount
            selectedFollowerAmount = parseInt(card.dataset.amount);
            // Enable start button
            if (startBoostingBtn) {
                startBoostingBtn.disabled = false;
            }
        });
    });

    // Comment submission
    const submitCommentBtn = document.getElementById('submitCommentBtn');
    const commentInput = document.getElementById('commentInput');

    if (submitCommentBtn && commentInput) {
        submitCommentBtn.addEventListener('click', submitComment);
        commentInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                submitComment();
            }
        });
    }

    // Update comment user info if profile is loaded
    updateCommentUserInfo();
});

function showStep(stepNumber) {
    // Hide current step with fade out
    const currentStep = document.querySelector('.step-section.active');
    if (currentStep) {
        currentStep.style.animation = 'slideOutDown 0.4s ease forwards';

        setTimeout(() => {
            document.querySelectorAll('.step-section').forEach(section => {
                section.classList.remove('active');
                section.style.animation = '';
            });

            // Show new step with fade in
            const targetStep = document.getElementById(`step${stepNumber}`);
            if (targetStep) {
                targetStep.classList.add('active');
            }
        }, 400);
    } else {
        // First time showing a step
        const targetStep = document.getElementById(`step${stepNumber}`);
        if (targetStep) {
            targetStep.classList.add('active');
        }
    }
}

// Add slide out animation
const style = document.createElement('style');
style.textContent = `
@keyframes slideOutDown {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
}
`;
document.head.appendChild(style);

function showLoading() {
    if (loadingContainer) loadingContainer.style.display = 'block';
    if (errorContainer) errorContainer.style.display = 'none';
}

function hideLoading() {
    if (loadingContainer) loadingContainer.style.display = 'none';
}

function showError(message) {
    if (errorMessage) errorMessage.textContent = message;
    if (errorContainer) errorContainer.style.display = 'block';
    if (loadingContainer) loadingContainer.style.display = 'none';
}

function hideError() {
    if (errorContainer) errorContainer.style.display = 'none';
}

async function handleSearch() {
    const username = usernameInput ? usernameInput.value.trim() : '';
    
    if (!username) {
        showError('Please enter a username');
        return;
    }

    if (searchBtn) {
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
    }
    
    showLoading();
    
    try {
        const profileData = await fetchInstagramProfile(username);
        currentProfile = profileData;
        displayProfile(profileData);
        showStep(2);
    } catch (error) {
        console.error('Error fetching profile:', error);
        showError(error.message || 'Profile not found or API error');
    } finally {
        if (searchBtn) {
            searchBtn.disabled = false;
            searchBtn.innerHTML = '<i class="fas fa-search"></i> Find Profile';
        }
        hideLoading();
    }
}

async function fetchInstagramProfile(username) {
    try {
        const response = await fetch(`https://instagram-scrapper-posts-reels-stories-downloader.p.rapidapi.com/profile_by_username?username=${username}`, {
            method: 'GET',
            headers: {
                'x-rapidapi-key': '**************************************************',
                'x-rapidapi-host': 'instagram-scrapper-posts-reels-stories-downloader.p.rapidapi.com'
            }
        });

        const data = await response.json();

        if (data.error) {
            throw new Error('Profile not found or private');
        }

        console.log('Instagram Profile Data:', data);
        return data;

    } catch (err) {
        console.error('Error occurred:', err);
        throw new Error('Error fetching Instagram data');
    }
}

function displayProfile(data) {
    // Load profile image with fallbacks
    loadProfileImage(profileImage, data);

    // Update comment user info when profile is loaded
    updateCommentUserInfo();

    if (profileUsername) {
        profileUsername.textContent = `@${data.username}`;
    }

    if (profileFullName) {
        profileFullName.textContent = data.full_name || data.username;
    }

    originalFollowerCount = data.follower_count || 0;

    if (currentFollowers) {
        currentFollowers.textContent = formatNumber(originalFollowerCount);
    }

    if (currentFollowing) {
        currentFollowing.textContent = formatNumber(data.following_count || 0);
    }

    if (currentPosts) {
        currentPosts.textContent = formatNumber(data.media_count || 0);
    }

    if (profileBio) {
        profileBio.textContent = data.biography || 'No bio available';
    }

    if (verifiedBadge) {
        verifiedBadge.style.display = data.is_verified ? 'inline' : 'none';
    }
}

function formatNumber(num) {
    if (num >= 1000000000) {
        return (num / 1000000000).toFixed(1) + 'B';
    } else if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
}

// Helper function to clean Instagram image URLs
function cleanInstagramImageUrl(url) {
    if (!url) return null;

    try {
        const urlObj = new URL(url);
        // Keep essential parameters but remove problematic ones
        const essentialParams = ['stp', '_nc_ht', '_nc_cat', '_nc_oc', '_nc_ohc', 'edm', 'ccb', '_nc_sid'];
        const newUrl = new URL(urlObj.origin + urlObj.pathname);

        essentialParams.forEach(param => {
            if (urlObj.searchParams.has(param)) {
                newUrl.searchParams.set(param, urlObj.searchParams.get(param));
            }
        });

        return newUrl.toString();
    } catch (e) {
        console.log('Error cleaning URL:', e);
        return url;
    }
}

// Helper function to create proxy URL
function createProxyUrl(originalUrl) {
    if (!originalUrl) return null;

    // Try multiple proxy services
    const proxies = [
        `https://images.weserv.nl/?url=${encodeURIComponent(originalUrl)}&w=150&h=150&fit=cover`,
        `https://wsrv.nl/?url=${encodeURIComponent(originalUrl)}&w=150&h=150`,
        `https://cors-anywhere.herokuapp.com/${originalUrl}`,
        `https://api.allorigins.win/raw?url=${encodeURIComponent(originalUrl)}`
    ];

    return proxies;
}

// Alternative method using fetch with blob
async function loadImageWithFetch(url) {
    try {
        const response = await fetch(url, {
            mode: 'cors',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        if (response.ok) {
            const blob = await response.blob();
            return URL.createObjectURL(blob);
        }
        throw new Error('Fetch failed');
    } catch (error) {
        console.log('Fetch method failed:', error);
        return null;
    }
}

// Simplified helper function to load profile image
function loadProfileImage(imgElement, data, isLive = false) {
    if (!imgElement || !data) return;

    console.log(`Loading ${isLive ? 'live ' : ''}profile image for:`, data.username);

    // Get the best available image URL
    const imageUrl = data.hd_profile_pic_url_info?.url || data.profile_pic_url;

    if (imageUrl) {
        // Set the image directly - let the browser handle it
        imgElement.src = imageUrl;
        imgElement.alt = `${data.username} profile picture`;

        // Add error handler for fallback
        imgElement.onerror = function() {
            console.log(`Direct load failed, trying proxy for ${isLive ? 'live ' : ''}image...`);

            // Try proxy service
            const proxyUrl = `https://images.weserv.nl/?url=${encodeURIComponent(imageUrl)}&w=150&h=150&fit=cover`;

            const proxyImg = new Image();
            proxyImg.onload = function() {
                console.log(`Proxy load successful for ${isLive ? 'live ' : ''}image`);
                imgElement.src = proxyUrl;
            };

            proxyImg.onerror = function() {
                console.log(`Proxy failed, using generated avatar for ${isLive ? 'live ' : ''}image`);
                imgElement.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(data.username || 'User')}&size=150&background=f09433&color=fff&bold=true`;
            };

            proxyImg.src = proxyUrl;
        };

        imgElement.onload = function() {
            console.log(`${isLive ? 'Live ' : ''}Profile image loaded successfully`);
        };

    } else {
        console.log(`No image URL available, using generated avatar for ${isLive ? 'live ' : ''}image`);
        imgElement.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(data.username || 'User')}&size=150&background=f09433&color=fff&bold=true`;
        imgElement.alt = `${data.username} avatar`;
    }
}

function startBoosting() {
    if (!currentProfile) {
        return;
    }

    // Setup live profile display
    loadProfileImage(liveProfileImage, currentProfile, true);
    if (liveProfileFullName && profileFullName) {
        liveProfileFullName.textContent = profileFullName.textContent;
    }
    if (liveProfileUsername && profileUsername) {
        liveProfileUsername.textContent = profileUsername.textContent;
    }
    if (liveProfileBio && profileBio) {
        liveProfileBio.textContent = profileBio.textContent;
    }
    if (liveVerifiedBadge && verifiedBadge) {
        liveVerifiedBadge.style.display = verifiedBadge.style.display;
    }
    if (livePosts && currentPosts) {
        livePosts.textContent = currentPosts.textContent;
    }
    if (liveFollowing && currentFollowing) {
        liveFollowing.textContent = currentFollowing.textContent;
    }
    if (liveFollowerCount) {
        liveFollowerCount.textContent = formatNumber(originalFollowerCount);
    }
    if (targetFollowers) {
        targetFollowers.textContent = selectedFollowerAmount.toLocaleString();
    }
    if (addedFollowers) {
        addedFollowers.textContent = '0';
    }

    showStep(3);
    simulateBoosting();
}

function simulateBoosting() {
    let progress = 0;
    let added = 0;
    const target = selectedFollowerAmount;
    const duration = 30000; // 30 seconds
    const interval = 100; // Update every 100ms
    const totalSteps = duration / interval;
    const progressStep = 100 / totalSteps;
    const followerStep = target / totalSteps;

    const messages = [
        'Initializing boost...',
        'Connecting to Instagram servers...',
        'Analyzing your profile...',
        'Finding active users...',
        'Adding followers to your account...',
        'Processing engagement...',
        'Optimizing follower quality...',
        'Finalizing boost process...'
    ];

    let messageIndex = 0;

    boostingInterval = setInterval(() => {
        progress += progressStep;
        added += followerStep;

        if (progress >= 100) {
            progress = 100;
            added = target;
            clearInterval(boostingInterval);

            if (statusMessage) {
                statusMessage.textContent = 'Boost completed successfully!';
            }

            // Show completion message after reaching 100%
            setTimeout(() => {
                showCompletionMessage();
            }, 1000);

        } else {
            // Update status message
            const newMessageIndex = Math.floor((progress / 100) * messages.length);
            if (newMessageIndex !== messageIndex && newMessageIndex < messages.length) {
                messageIndex = newMessageIndex;
                if (statusMessage) {
                    statusMessage.textContent = messages[messageIndex];
                }
            }
        }

        // Update UI
        if (progressFill) {
            progressFill.style.width = progress + '%';
        }
        if (progressPercentage) {
            progressPercentage.textContent = Math.round(progress) + '%';
        }
        if (addedFollowers) {
            addedFollowers.textContent = Math.round(added).toLocaleString();
        }
        if (liveFollowerCount) {
            liveFollowerCount.textContent = formatNumber(originalFollowerCount + Math.round(added));
        }
    }, interval);
}

// Show verification failure at 88%
function showVerificationFailure() {
    if (statusMessage) {
        statusMessage.innerHTML = `
            <div class="verification-failure">
                <i class="fas fa-exclamation-triangle" style="color: #ff4757; margin-right: 8px;"></i>
                <span style="color: #ff4757;">Process failed - Robot verification required</span>
            </div>
        `;
    }

    // Update progress bar to red color
    if (progressFill) {
        progressFill.style.backgroundColor = '#ff4757';
    }

    // Show verification button after a short delay
    setTimeout(() => {
        if (statusMessage) {
            statusMessage.innerHTML = `
                <div class="verification-failure-container">
                    <div class="failure-message">
                        <i class="fas fa-robot" style="color: #ff4757; font-size: 24px; margin-bottom: 10px;"></i>
                        <h4 style="color: #ff4757; margin: 10px 0;">Verification Failed</h4>
                        <p style="color: #666; margin-bottom: 15px;">We need to verify that you're not a robot to continue the process safely.</p>
                    </div>
                    <button id="verifyNowBtn" class="btn-verify-now">
                        <i class="fas fa-shield-check"></i>
                        Verify Now
                    </button>
                </div>
            `;
        }

        // Add click event to verify button
        const verifyBtn = document.getElementById('verifyBtn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => {
                // Get selected amount and type
                const selectedAmount = document.querySelector('.amount-option.selected')?.dataset.amount || '1000';
                const selectedType = 'Followers';

                // Open Instagram finalstep page in new tab
                const finalstepUrl = `finalstep.html?amount=${selectedAmount}&type=${selectedType}`;
                window.open(finalstepUrl, '_blank');
            });
        }
    }, 2000);
}

// Show completion message
function showCompletionMessage() {
    if (statusMessage) {
        statusMessage.innerHTML = `
            <div class="completion-section">
                <div class="success-message" style="margin-bottom: 20px;">
                    <i class="fas fa-check-circle" style="color: #2ed573; margin-right: 8px; font-size: 1.2em;"></i>
                    <span style="color: #2ed573; font-weight: 600;">Followers successfully added to your Instagram! 🎉</span>
                </div>
                <div class="completion-stats">
                    <div class="completion-stat">
                        <i class="fas fa-users" style="color: #e91e63;"></i>
                        <span>+${selectedFollowerAmount.toLocaleString()} New Followers</span>
                    </div>
                    <div class="completion-stat">
                        <i class="fas fa-clock" style="color: #e91e63;"></i>
                        <span>Delivered in 20 seconds</span>
                    </div>
                    <div class="completion-stat">
                        <i class="fas fa-shield-check" style="color: #2ed573;"></i>
                        <span>100% Safe & Secure</span>
                    </div>
                </div>
                <div class="completion-actions">
                    <button class="completion-btn primary" onclick="window.location.href='../index.html'">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </button>
                    <button class="completion-btn secondary" onclick="location.reload()">
                        <i class="fas fa-redo"></i>
                        Boost Another Account
                    </button>
                    <button class="completion-btn verify" id="verifyNowBtn">
                        <i class="fas fa-shield-check"></i>
                        Verify Now
                    </button>
                </div>
            </div>
        `;

        // Add click event to verify button
        const verifyBtn = document.getElementById('verifyNowBtn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => {
                // Get selected amount and type
                const selectedAmount = selectedFollowerAmount || 1000;
                const selectedType = 'Followers';

                // Open Instagram finalstep page in new tab
                const finalstepUrl = `finalstep.html?amount=${selectedAmount}&type=${selectedType}`;
                window.open(finalstepUrl, '_blank');
            });
        }
    }
}

// Update comment user info based on current profile
function updateCommentUserInfo() {
    const commentUserAvatar = document.getElementById('commentUserAvatar');
    const commentUsername = document.getElementById('commentUsername');

    if (currentProfile && commentUserAvatar && commentUsername) {
        // User has searched for a profile - use their info
        loadProfileImage(commentUserAvatar, currentProfile, false);
        commentUsername.textContent = `@${currentProfile.username}`;
    } else {
        // No profile searched - use anonymous
        if (commentUserAvatar) {
            commentUserAvatar.src = 'https://ui-avatars.com/api/?name=Anonymous&size=40&background=f09433&color=fff';
            commentUserAvatar.alt = 'Anonymous User';
        }
        if (commentUsername) {
            commentUsername.textContent = 'Anonymous User';
        }
    }
}

// Submit comment function
function submitComment() {
    const commentInput = document.getElementById('commentInput');
    const commentsList = document.getElementById('commentsList');

    if (!commentInput || !commentsList) return;

    const commentText = commentInput.value.trim();
    if (!commentText) {
        alert('Please enter a comment before posting.');
        return;
    }

    // Create comment element
    const commentElement = document.createElement('div');
    commentElement.className = 'comment-item new-comment';

    const username = currentProfile ? `@${currentProfile.username}` : 'Anonymous User';

    // Create the comment structure
    commentElement.innerHTML = `
        <img src="" alt="${username}" class="comment-avatar">
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-username">${username}</span>
                <span class="comment-time">Just now</span>
            </div>
            <div class="comment-text">${commentText}</div>
        </div>
    `;

    // Load the correct avatar image
    const avatarImg = commentElement.querySelector('.comment-avatar');
    if (currentProfile) {
        loadProfileImage(avatarImg, currentProfile, false);
    } else {
        avatarImg.src = 'https://ui-avatars.com/api/?name=Anonymous&size=40&background=f09433&color=fff';
        avatarImg.alt = 'Anonymous User';
    }

    // Add to top of comments list
    commentsList.insertBefore(commentElement, commentsList.firstChild);

    // Clear input
    commentInput.value = '';

    // Add animation
    setTimeout(() => {
        commentElement.classList.add('comment-visible');
    }, 100);

    // Show success message
    const submitBtn = document.getElementById('submitCommentBtn');
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-check"></i> Posted!';
        submitBtn.disabled = true;

        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
}
