# InstaBoost Pro - Instagram Follower Booster

A modern, interactive web application for simulating Instagram follower growth with a realistic user experience.

## 🌟 Features

### 🏠 **Homepage** (`index.html`)
- **Modern Design**: Gradient backgrounds with attractive colors
- **Dynamic Animations**: Smooth transitions and counter animations
- **Marketing Content**: Professional landing page with features, pricing, and testimonials
- **Responsive Design**: Works perfectly on all devices
- **Call-to-Action**: Multiple buttons leading to the booster functionality

### 🚀 **Follower Booster** (`booster.html`)
- **4-Step Process**: Intuitive user journey
- **Real API Integration**: Uses Instagram scraper API for authentic profile data
- **Live Simulation**: Realistic follower count animation
- **Package Selection**: Multiple follower packages (1K, 5K, 10K, 25K) + custom amount
- **Verification Flow**: Security-themed verification step (non-functional as requested)

## 📱 User Journey

### Step 1: Profile Search
- Enter Instagram username
- Real-time profile lookup using RapidAPI
- Error handling for invalid usernames

### Step 2: Package Selection
- Display actual profile information
- Choose from preset packages or enter custom amount
- Instagram-like profile card design

### Step 3: Boosting Process
- **Live Animation**: Followers count increases in real-time
- **Progress Bar**: Visual progress indicator
- **Statistics**: Added followers, target, time remaining
- **Realistic Timing**: 15-second simulation with random status updates

### Step 4: Verification Required
- **Security Message**: Suspicious activity detected
- **Non-functional Verify Button**: As requested
- **Professional Design**: Warning-style interface

## 🎨 Design Features

### Colors & Styling
- **Instagram Gradient**: Authentic orange-to-pink gradients
- **Modern Typography**: Poppins font family
- **Smooth Animations**: CSS transitions and keyframe animations
- **Glass Morphism**: Backdrop blur effects
- **Responsive Grid**: CSS Grid and Flexbox layouts

### Interactive Elements
- **Hover Effects**: Button and card interactions
- **Loading States**: Spinners and progress indicators
- **Form Validation**: Input validation with error messages
- **Smooth Scrolling**: Navigation and section transitions

## 🛠️ Technical Implementation

### API Integration
```javascript
// Updated Instagram API configuration
const API_URL = 'https://instagram-profile1.p.rapidapi.com/getprofile/';
const API_KEY = '**************************************************';
const API_HOST = 'instagram-profile1.p.rapidapi.com';

// Response structure:
{
  "id": "232192182",
  "username": "therock",
  "full_name": "Dwayne Johnson",
  "bio": "founder of stuff",
  "followers": 395137692,
  "following": 834,
  "is_verified": true,
  "profile_pic_url": "...",
  "profile_pic_url_hd": "...",
  "lastMedia": {
    "count": 7545
  }
}
```

### File Structure
```
instagram/
├── index.html          # Homepage (renamed from home.html)
├── styles.css          # Homepage styles (renamed from home-styles.css)
├── script.js           # Homepage JavaScript (renamed from home-script.js)
├── booster.html        # Follower booster page
├── booster-styles.css  # Booster page styles
├── booster-script.js   # Booster page JavaScript (fixed with script.js logic)
└── README.md           # This file
```

### Key Functions
- **Profile Fetching**: Same logic as original `script.js`
- **Number Formatting**: Consistent formatting across pages
- **Error Handling**: Comprehensive error management
- **Animation System**: Smooth transitions between steps

## 🚀 Getting Started

1. **Start Local Server**:
   ```bash
   python -m http.server 8080
   ```

2. **Open Homepage**:
   ```
   http://localhost:8080
   ```

3. **Test the Flow**:
   - Click "Start Boosting Now"
   - Enter a username (e.g., "instagram", "cristiano")
   - Select a follower package
   - Watch the live simulation
   - See the verification step

## 🔧 Customization

### API Configuration
Update the API key in `booster-script.js`:
```javascript
'x-rapidapi-key': 'YOUR_API_KEY_HERE'
```

### Styling
- Modify colors in CSS custom properties
- Adjust animation durations
- Change gradient combinations

### Simulation Settings
- Modify boosting duration (default: 15 seconds)
- Adjust follower packages
- Customize verification messages

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Tablet Support**: Perfect layout for tablets
- **Desktop Enhanced**: Full-featured desktop experience
- **Cross-browser**: Compatible with modern browsers

## 🎯 User Experience

- **Intuitive Navigation**: Clear step-by-step process
- **Visual Feedback**: Loading states and progress indicators
- **Error Handling**: User-friendly error messages
- **Professional Feel**: Instagram-like design language

## ⚠️ Disclaimer

This is a simulation tool for educational purposes. The verification button is intentionally non-functional as requested. This application is not affiliated with Instagram or Meta.

## 🔄 Updates Made

1. **Fixed API Integration**: Used same logic as original `script.js`
2. **Renamed Files**: `home.html` → `index.html` for main page
3. **Removed Redundant Files**: Cleaned up old files
4. **Enhanced Error Handling**: Added null checks for DOM elements
5. **Improved Animations**: Smooth transitions and realistic timing

---

**Built with ❤️ for realistic Instagram growth simulation**
