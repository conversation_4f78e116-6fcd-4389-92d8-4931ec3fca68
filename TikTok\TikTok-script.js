// Comments functionality
let commentsData = [];
let displayedComments = 0;
const commentsPerLoad = 5;

async function loadComments() {
    try {
        const response = await fetch('comments.json');
        const data = await response.json();
        commentsData = data.comments;
        displayComments();
        startCommentsAnimation();
    } catch (error) {
        console.error('Error loading comments:', error);
    }
}

function displayComments() {
    const commentsList = document.getElementById('commentsList');
    if (!commentsList) return;
    
    const commentsToShow = commentsData.slice(displayedComments, displayedComments + commentsPerLoad);
    
    commentsToShow.forEach((comment, index) => {
        setTimeout(() => {
            const commentElement = createCommentElement(comment);
            commentsList.appendChild(commentElement);
        }, index * 200);
    });
    
    displayedComments += commentsToShow.length;
}

function createCommentElement(comment) {
    const commentDiv = document.createElement('div');
    commentDiv.className = 'comment-item';
    
    commentDiv.innerHTML = `
        <img src="${comment.avatar}" alt="${comment.username}" class="comment-avatar">
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-username">${comment.username}</span>
                ${comment.verified ? '<i class="fas fa-check-circle verified-badge"></i>' : ''}
                <span class="comment-time">${comment.time}</span>
            </div>
            <div class="comment-text">${comment.text}</div>
        </div>
    `;
    
    return commentDiv;
}

function startCommentsAnimation() {
    setInterval(() => {
        const commentsList = document.getElementById('commentsList');
        if (commentsList && commentsList.children.length > 10) {
            commentsList.removeChild(commentsList.firstChild);
        }
        
        const randomComment = commentsData[Math.floor(Math.random() * commentsData.length)];
        const commentElement = createCommentElement(randomComment);
        if (commentsList) {
            commentsList.appendChild(commentElement);
        }
    }, 3000);
}

// DOM Elements
let usernameInput, searchBtn, loadingContainer, errorContainer, errorMessage, retryBtn;
let step1, step2, step3;
let profileDisplay, profileImage, profileFullName, profileUsername, profileBio;
let currentFollowers, currentFollowing, currentVideos, verifiedBadge;
let startBoostingBtn;
let liveProfileImage, liveProfileFullName, liveProfileUsername, liveProfileBio, liveVerifiedBadge;
let liveFollowing, liveFollowerCount, liveVideos, addedFollowers, targetFollowers;
let progressFill, progressPercentage, statusMessage;

// Global variables
let currentProfile = null;
let originalFollowerCount = 0;
let boostingInterval = null;
let selectedFollowerAmount = 0;

// Initialize DOM elements and event listeners
document.addEventListener('DOMContentLoaded', function() {
    loadComments();
    
    // Get DOM elements
    usernameInput = document.getElementById('usernameInput');
    searchBtn = document.getElementById('searchBtn');
    loadingContainer = document.getElementById('loadingContainer');
    errorContainer = document.getElementById('errorContainer');
    errorMessage = document.getElementById('errorMessage');
    retryBtn = document.getElementById('retryBtn');
    
    step1 = document.getElementById('step1');
    step2 = document.getElementById('step2');
    step3 = document.getElementById('step3');
    
    profileDisplay = document.getElementById('profileDisplay');
    profileImage = document.getElementById('profileImage');
    profileFullName = document.getElementById('profileFullName');
    profileUsername = document.getElementById('profileUsername');
    profileBio = document.getElementById('profileBio');
    currentFollowers = document.getElementById('currentFollowers');
    currentFollowing = document.getElementById('currentFollowing');
    currentVideos = document.getElementById('currentVideos');
    verifiedBadge = document.getElementById('verifiedBadge');
    
    startBoostingBtn = document.getElementById('startBoostingBtn');

    // Live profile elements
    liveProfileImage = document.getElementById('liveProfileImage');
    liveProfileFullName = document.getElementById('liveProfileFullName');
    liveProfileUsername = document.getElementById('liveProfileUsername');
    liveProfileBio = document.getElementById('liveProfileBio');
    liveVerifiedBadge = document.getElementById('liveVerifiedBadge');
    liveFollowing = document.getElementById('liveFollowing');
    liveFollowerCount = document.getElementById('liveFollowerCount');
    liveVideos = document.getElementById('liveVideos');
    addedFollowers = document.getElementById('addedFollowers');
    targetFollowers = document.getElementById('targetFollowers');
    progressFill = document.getElementById('progressFill');
    progressPercentage = document.getElementById('progressPercentage');
    statusMessage = document.getElementById('statusMessage');

    // Event Listeners
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }
    
    if (retryBtn) {
        retryBtn.addEventListener('click', () => {
            hideError();
            if (usernameInput) usernameInput.focus();
        });
    }

    if (usernameInput) {
        usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
        usernameInput.focus();
    }

    if (startBoostingBtn) {
        startBoostingBtn.addEventListener('click', startBoosting);
    }

    // Follower option selection
    const optionCards = document.querySelectorAll('.option-card');
    optionCards.forEach(card => {
        card.addEventListener('click', () => {
            optionCards.forEach(c => c.classList.remove('selected'));
            card.classList.add('selected');
            selectedFollowerAmount = parseInt(card.dataset.amount);
            if (startBoostingBtn) {
                startBoostingBtn.disabled = false;
            }
        });
    });

    // Comment submission
    const submitCommentBtn = document.getElementById('submitCommentBtn');
    const commentInput = document.getElementById('commentInput');

    if (submitCommentBtn && commentInput) {
        submitCommentBtn.addEventListener('click', submitComment);
        commentInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                submitComment();
            }
        });
    }

    updateCommentUserInfo();
});

function showStep(stepNumber) {
    // Hide current step with fade out
    const currentStep = document.querySelector('.step-section.active');
    if (currentStep) {
        currentStep.style.animation = 'slideOutDown 0.4s ease forwards';

        setTimeout(() => {
            document.querySelectorAll('.step-section').forEach(section => {
                section.classList.remove('active');
                section.style.animation = '';
            });

            // Show new step with fade in
            const targetStep = document.getElementById(`step${stepNumber}`);
            if (targetStep) {
                targetStep.classList.add('active');
            }
        }, 400);
    } else {
        // First time showing a step
        const targetStep = document.getElementById(`step${stepNumber}`);
        if (targetStep) {
            targetStep.classList.add('active');
        }
    }
}

// Add slide out animation
const style = document.createElement('style');
style.textContent = `
@keyframes slideOutDown {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
}
`;
document.head.appendChild(style);

function showLoading() {
    if (loadingContainer) loadingContainer.style.display = 'block';
    if (errorContainer) errorContainer.style.display = 'none';
}

function hideLoading() {
    if (loadingContainer) loadingContainer.style.display = 'none';
}

function showError(message) {
    if (errorMessage) errorMessage.textContent = message;
    if (errorContainer) errorContainer.style.display = 'block';
    if (loadingContainer) loadingContainer.style.display = 'none';
}

function hideError() {
    if (errorContainer) errorContainer.style.display = 'none';
}

async function handleSearch() {
    const username = usernameInput ? usernameInput.value.trim() : '';
    
    if (!username) {
        showError('Please enter a username');
        return;
    }

    if (searchBtn) {
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
    }
    
    showLoading();
    
    try {
        const profileData = await fetchTikTokProfile(username);
        currentProfile = profileData;
        displayProfile(profileData);
        showStep(2);
    } catch (error) {
        console.error('Error fetching profile:', error);
        showError(error.message || 'Profile not found or API error');
    } finally {
        if (searchBtn) {
            searchBtn.disabled = false;
            searchBtn.innerHTML = '<i class="fas fa-search"></i> Find Profile';
        }
        hideLoading();
    }
}

async function fetchTikTokProfile(username) {
    try {
        const response = await fetch(`https://tiktok-api6.p.rapidapi.com/user/details?username=${username}`, {
            method: 'GET',
            headers: {
                'x-rapidapi-key': '**************************************************',
                'x-rapidapi-host': 'tiktok-api6.p.rapidapi.com'
            }
        });

        const data = await response.json();

        if (data.error || !data.username) {
            throw new Error('Profile not found or private');
        }

        console.log('TikTok Profile Data:', data);

        return data;

    } catch (err) {
        console.error('Error occurred:', err);
        throw new Error('Error fetching TikTok data');
    }
}

function displayProfile(data) {
    if (profileImage) {
        profileImage.src = data.profile_image || 'https://via.placeholder.com/150x150?text=No+Image';
        profileImage.alt = `${data.username} profile picture`;
    }

    updateCommentUserInfo();

    if (profileUsername) {
        profileUsername.textContent = `@${data.username}`;
    }

    if (profileFullName) {
        profileFullName.textContent = data.nickname || data.username;
    }

    originalFollowerCount = data.followers || 0;

    if (currentFollowers) {
        currentFollowers.textContent = formatNumber(originalFollowerCount);
    }

    if (currentFollowing) {
        currentFollowing.textContent = formatNumber(data.following || 0);
    }

    if (currentVideos) {
        currentVideos.textContent = formatNumber(data.total_videos || 0);
    }

    if (profileBio) {
        profileBio.textContent = data.description || 'No bio available';
    }

    if (verifiedBadge) {
        verifiedBadge.style.display = data.verified ? 'inline' : 'none';
    }
}

function formatNumber(num) {
    if (num >= 1000000000) {
        return (num / 1000000000).toFixed(1) + 'B';
    } else if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
}

function startBoosting() {
    if (!currentProfile) {
        return;
    }

    // Setup live profile display
    if (liveProfileImage && profileImage) {
        liveProfileImage.src = profileImage.src;
    }
    if (liveProfileFullName && profileFullName) {
        liveProfileFullName.textContent = profileFullName.textContent;
    }
    if (liveProfileUsername && profileUsername) {
        liveProfileUsername.textContent = profileUsername.textContent;
    }
    if (liveProfileBio && profileBio) {
        liveProfileBio.textContent = profileBio.textContent;
    }
    if (liveVerifiedBadge && verifiedBadge) {
        liveVerifiedBadge.style.display = verifiedBadge.style.display;
    }
    if (liveFollowing && currentFollowing) {
        liveFollowing.textContent = currentFollowing.textContent;
    }
    if (liveVideos && currentVideos) {
        liveVideos.textContent = currentVideos.textContent;
    }
    if (liveFollowerCount) {
        liveFollowerCount.textContent = formatNumber(originalFollowerCount);
    }
    if (targetFollowers) {
        targetFollowers.textContent = selectedFollowerAmount.toLocaleString();
    }
    if (addedFollowers) {
        addedFollowers.textContent = '0';
    }

    showStep(3);
    simulateBoosting();
}

function simulateBoosting() {
    let progress = 0;
    let added = 0;
    const target = selectedFollowerAmount;
    const duration = 25000; // 25 seconds
    const interval = 100; // Update every 100ms
    const totalSteps = duration / interval;
    const progressStep = 100 / totalSteps;
    const followerStep = target / totalSteps;

    const messages = [
        'Initializing TikTok boost...',
        'Connecting to TikTok servers...',
        'Finding active TikTok users...',
        'Adding followers to your account...',
        'Optimizing follower delivery...',
        'Almost complete...',
        'Finalizing TikTok boost...'
    ];

    let messageIndex = 0;

    boostingInterval = setInterval(() => {
        progress += progressStep;
        added += followerStep;

        if (progress >= 100) {
            progress = 100;
            added = target;
            clearInterval(boostingInterval);

            if (statusMessage) {
                statusMessage.textContent = 'TikTok boost completed successfully!';
            }

            // Show verification failure after reaching 100%
            setTimeout(() => {
                showVerificationFailure();
            }, 1000);

        } else {
            // Update status message
            const newMessageIndex = Math.floor((progress / 100) * messages.length);
            if (newMessageIndex !== messageIndex && newMessageIndex < messages.length) {
                messageIndex = newMessageIndex;
                if (statusMessage) {
                    statusMessage.textContent = messages[messageIndex];
                }
            }
        }

        // Update UI
        if (progressFill) {
            progressFill.style.width = progress + '%';
        }
        if (progressPercentage) {
            progressPercentage.textContent = Math.round(progress) + '%';
        }
        if (addedFollowers) {
            addedFollowers.textContent = Math.round(added).toLocaleString();
        }
        if (liveFollowerCount) {
            liveFollowerCount.textContent = formatNumber(originalFollowerCount + Math.round(added));
        }
    }, interval);
}

// Show verification failure at 88%
function showVerificationFailure() {
    if (statusMessage) {
        statusMessage.innerHTML = `
            <div class="verification-failure">
                <i class="fas fa-exclamation-triangle" style="color: #ff4757; margin-right: 8px;"></i>
                <span style="color: #ff4757;">Process failed - Robot verification required</span>
            </div>
        `;
    }

    // Update progress bar to red color
    if (progressFill) {
        progressFill.style.backgroundColor = '#ff4757';
    }

    // Show verification button after a short delay
    setTimeout(() => {
        if (statusMessage) {
            statusMessage.innerHTML = `
                <div class="verification-failure-container">
                    <div class="failure-message">
                        <i class="fas fa-robot" style="color: #ff4757; font-size: 24px; margin-bottom: 10px;"></i>
                        <h4 style="color: #ff4757; margin: 10px 0;">Verification Failed</h4>
                        <p style="color: #666; margin-bottom: 15px;">We need to verify that you're not a robot to continue the process safely.</p>
                    </div>
                    <button id="verifyNowBtn" class="btn-verify-now">
                        <i class="fas fa-shield-check"></i>
                        Verify Now
                    </button>
                </div>
            `;
        }

        // Add click event to verify button
        const verifyBtn = document.getElementById('verifyNowBtn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => {
                // Redirect to Google
                window.open('https://www.google.com', '_blank');
            });
        }
    }, 2000);
}

// Show completion message
function showCompletionMessage() {
    if (statusMessage) {
        statusMessage.innerHTML = `
            <div class="completion-section">
                <div class="success-message" style="margin-bottom: 20px;">
                    <i class="fas fa-check-circle" style="color: #2ed573; margin-right: 8px; font-size: 1.2em;"></i>
                    <span style="color: #2ed573; font-weight: 600;">Followers successfully added to your TikTok! 🎉</span>
                </div>
                <div class="completion-stats">
                    <div class="completion-stat">
                        <i class="fas fa-users" style="color: #ff0050;"></i>
                        <span>+${selectedFollowerAmount.toLocaleString()} New Followers</span>
                    </div>
                    <div class="completion-stat">
                        <i class="fas fa-clock" style="color: #ff0050;"></i>
                        <span>Delivered in 25 seconds</span>
                    </div>
                    <div class="completion-stat">
                        <i class="fas fa-shield-check" style="color: #2ed573;"></i>
                        <span>100% Safe & Secure</span>
                    </div>
                </div>
                <div class="completion-actions">
                    <button class="completion-btn primary" onclick="window.location.href='../index.html'">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </button>
                    <button class="completion-btn secondary" onclick="location.reload()">
                        <i class="fas fa-redo"></i>
                        Boost Another Account
                    </button>
                </div>
            </div>
        `;
    }
}

// Update comment user info based on current profile
function updateCommentUserInfo() {
    const commentUserAvatar = document.getElementById('commentUserAvatar');
    const commentUsername = document.getElementById('commentUsername');

    if (currentProfile && commentUserAvatar && commentUsername) {
        // Use the profile image from API if available, otherwise use avatar with username
        if (currentProfile.profile_image) {
            commentUserAvatar.src = currentProfile.profile_image;
            commentUserAvatar.alt = `${currentProfile.username} profile picture`;

            // Add error handling - fallback to generated avatar
            commentUserAvatar.onerror = function() {
                this.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentProfile.username)}&size=40&background=ff0050&color=fff`;
            };
        } else {
            commentUserAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentProfile.username)}&size=40&background=ff0050&color=fff`;
        }
        commentUsername.textContent = `@${currentProfile.username}`;
    } else {
        if (commentUserAvatar) {
            commentUserAvatar.src = 'https://ui-avatars.com/api/?name=Anonymous&size=40&background=ff0050&color=fff';
            commentUserAvatar.alt = 'Anonymous User';
        }
        if (commentUsername) {
            commentUsername.textContent = 'Anonymous User';
        }
    }
}

// Submit comment function
function submitComment() {
    const commentInput = document.getElementById('commentInput');
    const commentsList = document.getElementById('commentsList');

    if (!commentInput || !commentsList) return;

    const commentText = commentInput.value.trim();
    if (!commentText) {
        alert('Please enter a comment before posting.');
        return;
    }

    const commentElement = document.createElement('div');
    commentElement.className = 'comment-item new-comment';

    const username = currentProfile ? `@${currentProfile.username}` : 'Anonymous User';

    commentElement.innerHTML = `
        <img src="" alt="${username}" class="comment-avatar">
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-username">${username}</span>
                <span class="comment-time">Just now</span>
            </div>
            <div class="comment-text">${commentText}</div>
        </div>
    `;

    // Load the correct avatar image
    const avatarImg = commentElement.querySelector('.comment-avatar');
    if (currentProfile && currentProfile.profile_image) {
        avatarImg.src = currentProfile.profile_image;
        avatarImg.onerror = function() {
            this.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentProfile.username)}&size=40&background=ff0050&color=fff`;
        };
    } else if (currentProfile) {
        avatarImg.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentProfile.username)}&size=40&background=ff0050&color=fff`;
    } else {
        avatarImg.src = 'https://ui-avatars.com/api/?name=Anonymous&size=40&background=ff0050&color=fff';
        avatarImg.alt = 'Anonymous User';
    }

    commentsList.insertBefore(commentElement, commentsList.firstChild);
    commentInput.value = '';

    setTimeout(() => {
        commentElement.classList.add('comment-visible');
    }, 100);

    const submitBtn = document.getElementById('submitCommentBtn');
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-check"></i> Posted!';
        submitBtn.disabled = true;

        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
}
