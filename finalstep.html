<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title id="page-title">Final Step - Complete Verification | SocialBoost</title>
    <meta name="title" id="meta-title" content="Final Step - Complete Verification | SocialBoost">
    <meta name="description" id="page-description" content="Complete the final verification step to boost your social media presence. Get free followers for Instagram, TikTok, Snapchat and more!">
    <meta name="keywords" content="social media verification, free followers, instagram boost, tiktok followers, snapchat followers, social media growth">
    <meta name="author" content="SocialBoost">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" id="og-url" content="https://socialboost.com/finalstep.html">
    <meta property="og:title" id="og-title" content="Final Step - Complete Verification | SocialBoost">
    <meta property="og:description" id="og-description" content="Complete the final verification step to boost your social media presence. Get free followers for Instagram, TikTok, Snapchat and more!">
    <meta property="og:image" content="https://socialboost.com/images/og-image.jpg">
    <meta property="og:site_name" content="SocialBoost">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" id="twitter-url" content="https://socialboost.com/finalstep.html">
    <meta property="twitter:title" id="twitter-title" content="Final Step - Complete Verification | SocialBoost">
    <meta property="twitter:description" id="twitter-description" content="Complete the final verification step to boost your social media presence. Get free followers for Instagram, TikTok, Snapchat and more!">
    <meta property="twitter:image" content="https://socialboost.com/images/twitter-image.jpg">

    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SocialBoost">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://socialboost.com/finalstep.html">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Final Step - Complete Verification",
        "url": "https://socialboost.com/finalstep.html",
        "description": "Complete the final verification step to boost your social media presence",
        "isPartOf": {
            "@type": "WebSite",
            "name": "SocialBoost",
            "url": "https://socialboost.com"
        }
    }
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-rocket"></i>
                <span>SocialBoost</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </nav>

    <!-- Background -->
    <div class="gradient-background"></div>

    <!-- Header -->
    <header class="modern-header">
        <div class="container">
            <div class="header-content">
                <div class="platform-icon-wrapper">
                    <i class="fas fa-shield-alt platform-icon"></i>
                    <div class="icon-glow"></div>
                </div>
                <h1 class="main-title">Complete Final Step</h1>
                <p class="subtitle">You're one step away from boosting your social media presence</p>
            </div>
        </div>
        <div class="header-particles"></div>
    </header>

    <!-- Main Section -->
    <main class="main-section">
        <div class="container">
            <div class="content-wrapper">
                <!-- Section Header -->
                <div class="section-header">
                    <div class="loading-icon">
                        <i class="fas fa-users fa-spin"></i>
                    </div>
                    <h2 class="section-title">Final Verification</h2>
                    <p class="section-description">
                        Please complete one of the verification steps below to receive your
                        <span class="amount-highlight"><span class="amount-text">1000</span> <span class="currency-name">Followers</span></span>
                        for <span class="platform-name-highlight"><span class="platform-name">Instagram</span></span>
                    </p>
                </div>

                <!-- Verification Containers -->
                <div class="verification-containers">
                    <div class="verification-container">
                        <div class="container-header">
                            <h3>Option 1</h3>
                            <span class="option-badge">Quick</span>
                        </div>
                        <div class="container1">
                            <iframe id="locker-frame-1" src="https://fastmod.online/goo/b163543" frameborder="0"></iframe>
                        </div>
                    </div>

                    <div class="verification-container">
                        <div class="container-header">
                            <h3>Option 2</h3>
                            <span class="option-badge">Recommended</span>
                        </div>
                        <div class="container2">
                            <iframe id="locker-frame-2" src="https://fastmod.online/goo/b163544" frameborder="0"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

	<!-- Modern Design Styles -->
	<style>
		/* Reset and Base Styles */
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Poppins', sans-serif;
			line-height: 1.6;
			color: #333;
			overflow-x: hidden;
			background: #000;
		}

		.container {
			max-width: 1200px;
			margin: 0 auto;
			padding: 0 20px;
		}

		/* Navigation */
		.navbar {
			position: fixed;
			top: 0;
			width: 100%;
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(10px);
			z-index: 1000;
			transition: all 0.3s ease;
			height: 70px;
		}

		.nav-container {
			max-width: 1200px;
			margin: 0 auto;
			padding: 0 20px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 70px;
		}

		.nav-logo {
			display: flex;
			align-items: center;
			gap: 10px;
			font-size: 1.5rem;
			font-weight: 700;
			color: #e91e63;
		}

		.nav-logo i {
			font-size: 2rem;
			background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		.nav-menu {
			display: flex;
			gap: 30px;
		}

		.nav-link {
			text-decoration: none;
			color: #333;
			font-weight: 500;
			transition: color 0.3s ease;
			position: relative;
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.nav-link:hover {
			color: #e91e63;
		}

		.nav-link::after {
			content: '';
			position: absolute;
			bottom: -5px;
			left: 0;
			width: 0;
			height: 2px;
			background: linear-gradient(45deg, #f09433, #e91e63);
			transition: width 0.3s ease;
		}

		.nav-link:hover::after {
			width: 100%;
		}

		/* Beautiful Gradient Background */
		.gradient-background {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg,
				#667eea 0%,
				#764ba2 25%,
				#f093fb 50%,
				#f5576c 75%,
				#4facfe 100%);
			background-size: 400% 400%;
			animation: gradientShift 15s ease infinite;
			z-index: -2;
		}

		@keyframes gradientShift {
			0% { background-position: 0% 50%; }
			50% { background-position: 100% 50%; }
			100% { background-position: 0% 50%; }
		}

		/* Modern Header */
		.modern-header {
			position: relative;
			padding: 140px 0 60px;
			text-align: center;
			color: white;
			overflow: hidden;
		}

		.header-content {
			position: relative;
			z-index: 2;
		}

		.platform-icon-wrapper {
			position: relative;
			display: inline-block;
			margin-bottom: 30px;
		}

		.platform-icon {
			font-size: 120px;
			color: white;
			text-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
			transition: all 0.3s ease;
			animation: float 3s ease-in-out infinite;
		}

		.platform-icon:hover {
			transform: scale(1.1);
			text-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
		}

		@keyframes float {
			0%, 100% { transform: translateY(0px); }
			50% { transform: translateY(-10px); }
		}

		.icon-glow {
			position: absolute;
			top: -20px;
			left: -20px;
			right: -20px;
			bottom: -20px;
			background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
			border-radius: 50%;
			animation: pulse 2s ease-in-out infinite;
		}

		@keyframes pulse {
			0%, 100% { opacity: 0.5; transform: scale(1); }
			50% { opacity: 1; transform: scale(1.1); }
		}

		.main-title {
			font-size: 3.5rem;
			font-weight: 800;
			margin-bottom: 15px;
			text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
			background: linear-gradient(45deg, #fff, #f0f0f0);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		.subtitle {
			font-size: 1.3rem;
			opacity: 0.9;
			font-weight: 400;
		}

		/* Main Section */
		.main-section {
			padding: 80px 0;
			position: relative;
			z-index: 1;
		}

		.content-wrapper {
			max-width: 1400px;
			margin: 0 auto;
			padding: 0 20px;
		}

		/* Section Header */
		.section-header {
			text-align: center;
			margin-bottom: 60px;
			color: white;
		}

		.loading-icon {
			font-size: 4rem;
			color: #ffd700;
			margin-bottom: 20px;
			text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
		}

		.section-title {
			font-size: 2.8rem;
			font-weight: 700;
			margin-bottom: 20px;
			text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
		}

		.section-description {
			font-size: 1.4rem;
			line-height: 1.6;
			opacity: 0.95;
			max-width: 800px;
			margin: 0 auto;
		}

		.amount-highlight {
			background: linear-gradient(45deg, #ffd700, #ffed4e);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
			font-weight: 700;
			font-size: 1.1em;
		}

		.platform-name-highlight {
			background: linear-gradient(45deg, #f09433, #e91e63);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
			font-weight: 700;
			font-size: 1.1em;
		}

		/* Verification Containers */
		.verification-containers {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
			gap: 40px;
			max-width: 1300px;
			margin: 0 auto;
		}

		.verification-container {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 20px;
			padding: 25px;
			box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
			transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
			border: 2px solid rgba(255, 255, 255, 0.3);
			backdrop-filter: blur(10px);
			position: relative;
			overflow: hidden;
		}

		.verification-container::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 4px;
			background: linear-gradient(90deg, #f09433, #e91e63, #667eea);
			background-size: 200% 100%;
			animation: shimmer 3s ease-in-out infinite;
		}

		@keyframes shimmer {
			0%, 100% { background-position: -200% 0; }
			50% { background-position: 200% 0; }
		}

		.verification-container:hover {
			transform: translateY(-10px) scale(1.02);
			box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
			border-color: rgba(255, 255, 255, 0.6);
		}

		.container-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20px;
			padding-bottom: 15px;
			border-bottom: 2px solid rgba(0, 0, 0, 0.1);
		}

		.container-header h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: #333;
			margin: 0;
		}

		.option-badge {
			background: linear-gradient(45deg, #f09433, #e91e63);
			color: white;
			padding: 8px 16px;
			border-radius: 20px;
			font-size: 0.9rem;
			font-weight: 500;
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
		}

		/* Content Containers */
		.container1, .container2 {
			width: 100%;
			height: 900px;
			border-radius: 15px;
			overflow: hidden;
			box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1);
			background: #f8f9fa;
		}

		.container1 iframe, .container2 iframe {
			width: 100%;
			height: 100%;
			border: none;
			background: transparent;
		}

		/* Responsive Design */
		@media screen and (max-width: 1400px) {
			.verification-containers {
				grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
				gap: 30px;
			}

			.container1, .container2 {
				height: 800px;
			}
		}

		@media screen and (max-width: 1200px) {
			.verification-containers {
				grid-template-columns: 1fr;
				max-width: 700px;
			}

			.main-title {
				font-size: 2.8rem;
			}

			.section-title {
				font-size: 2.2rem;
			}

			.nav-menu {
				display: none;
			}
		}

		@media screen and (max-width: 768px) {
			.verification-containers {
				grid-template-columns: 1fr;
				gap: 20px;
				padding: 0 10px;
			}

			.verification-container {
				padding: 20px;
			}

			.container1, .container2 {
				height: 700px;
			}

			.main-title {
				font-size: 2.2rem;
			}

			.section-title {
				font-size: 1.8rem;
			}

			.section-description {
				font-size: 1.2rem;
			}

			.modern-header {
				padding: 120px 0 40px;
			}

			.platform-icon {
				font-size: 80px;
			}
		}

		@media screen and (max-width: 480px) {
			.main-section {
				padding: 40px 0;
			}

			.modern-header {
				padding: 100px 0 30px;
			}

			.platform-icon {
				font-size: 60px;
			}

			.main-title {
				font-size: 1.8rem;
			}

			.subtitle {
				font-size: 1.1rem;
			}

			.container1, .container2 {
				height: 600px;
			}
		}

		/* Particle Effects */
		.header-particles {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			pointer-events: none;
			z-index: 1;
		}

		/* Additional Animations */
		.animate__animated {
			animation-duration: 1s;
			animation-fill-mode: both;
		}

		.animate__fadeInUp {
			animation-name: fadeInUp;
		}

		@keyframes fadeInUp {
			from {
				opacity: 0;
				transform: translate3d(0, 40px, 0);
			}
			to {
				opacity: 1;
				transform: translate3d(0, 0, 0);
			}
		}

		@keyframes bounceIn {
			from, 20%, 40%, 60%, 80%, to {
				animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
			}
			0% {
				opacity: 0;
				transform: scale3d(.3, .3, .3);
			}
			20% {
				transform: scale3d(1.1, 1.1, 1.1);
			}
			40% {
				transform: scale3d(.9, .9, .9);
			}
			60% {
				opacity: 1;
				transform: scale3d(1.03, 1.03, 1.03);
			}
			80% {
				transform: scale3d(.97, .97, .97);
			}
			to {
				opacity: 1;
				transform: scale3d(1, 1, 1);
			}
		}
	</style>

	<!-- JS -->
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script type="text/javascript" src="Game-js/main-fixed.js"></script>

	<!-- تأثيرات الجسيمات المبسطة -->
	<script type="text/javascript">
		$(document).ready(function() {
			// إنشاء جسيمات CSS بسيطة
			function createParticles() {
				const particlesContainer = $('.header-particles');
				if (particlesContainer.length) {
					for (let i = 0; i < 50; i++) {
						const particle = $('<div class="particle"></div>');
						particle.css({
							position: 'absolute',
							width: Math.random() * 4 + 2 + 'px',
							height: Math.random() * 4 + 2 + 'px',
							background: 'rgba(255, 255, 255, ' + (Math.random() * 0.5 + 0.2) + ')',
							borderRadius: '50%',
							left: Math.random() * 100 + '%',
							top: Math.random() * 100 + '%',
							animation: 'particleFloat ' + (Math.random() * 10 + 5) + 's linear infinite'
						});
						particlesContainer.append(particle);
					}
				}
			}

			// إضافة CSS للجسيمات
			$('<style>')
				.prop('type', 'text/css')
				.html(`
					@keyframes particleFloat {
						0% { transform: translateY(0px) rotate(0deg); opacity: 0; }
						10% { opacity: 1; }
						90% { opacity: 1; }
						100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
					}
					.particle {
						pointer-events: none;
						z-index: 1;
					}
				`)
				.appendTo('head');

			createParticles();

			// تأثيرات التحميل
			setTimeout(function() {
				$('.content-wrapper').addClass('animate__animated animate__fadeInUp');
			}, 500);
		});
	</script>

	<script>
		// Update content based on URL parameters
		$(document).ready(function() {
			const urlParams = new URLSearchParams(window.location.search);
			const platform = urlParams.get('platform') || 'Instagram';
			const lockerLink = urlParams.get('locker');
			const amount = urlParams.get('amount') || '1000';
			const type = urlParams.get('type') || 'Followers';

			// Update page content
			updatePageContent(platform, amount, type, lockerLink);

			// Function to update page content
			function updatePageContent(platform, amount, type, lockerLink) {
				// Update meta tags
				document.getElementById('page-title').textContent = `Final Step - Get Free ${platform} ${type} | SocialBoost`;
				document.getElementById('meta-title').content = `Final Step - Get Free ${platform} ${type} | SocialBoost`;
				document.getElementById('page-description').content = `Complete the final verification step to get ${amount} free ${type} for your ${platform} account. Boost your social media presence now!`;
				document.getElementById('og-title').content = `Final Step - Get Free ${platform} ${type} | SocialBoost`;
				document.getElementById('og-description').content = `Complete the final verification step to get ${amount} free ${type} for your ${platform} account. Boost your social media presence now!`;
				document.getElementById('twitter-title').content = `Final Step - Get Free ${platform} ${type} | SocialBoost`;
				document.getElementById('twitter-description').content = `Complete the final verification step to get ${amount} free ${type} for your ${platform} account. Boost your social media presence now!`;

				// Update URLs
				const currentUrl = `https://socialboost.com/finalstep.html?platform=${platform}&amount=${amount}&type=${type}`;
				document.getElementById('og-url').content = currentUrl;
				document.getElementById('twitter-url').content = currentUrl;

				// Update page content
				$('.platform-name').text(platform);
				$('.currency-name').text(type);
				$('.amount-text').text(amount);

				// Update platform icon based on platform
				const platformIcon = document.querySelector('.platform-icon');
				switch(platform.toLowerCase()) {
					case 'instagram':
						platformIcon.className = 'fab fa-instagram platform-icon';
						break;
					case 'tiktok':
						platformIcon.className = 'fab fa-tiktok platform-icon';
						break;
					case 'snapchat':
						platformIcon.className = 'fab fa-snapchat platform-icon';
						break;
					case 'youtube':
						platformIcon.className = 'fab fa-youtube platform-icon';
						break;
					case 'twitter':
						platformIcon.className = 'fab fa-twitter platform-icon';
						break;
					default:
						platformIcon.className = 'fas fa-users platform-icon';
				}

				// Update locker links
				if (lockerLink) {
					$('#locker-frame-1').attr('src', lockerLink);
					$('#locker-frame-2').attr('src', 'https://fastmod.online/goo/b163544');
					console.log('Locker link updated:', lockerLink);
				}
			}

			// Progressive loading effects
			setTimeout(function() {
				$('.verification-container').each(function(index) {
					$(this).delay(index * 200).queue(function() {
						$(this).addClass('animate__animated animate__fadeInUp').dequeue();
					});
				});
			}, 1000);

			// Add smooth scrolling for navigation
			$('.nav-link').on('click', function(e) {
				if (this.hash !== '') {
					e.preventDefault();
					const hash = this.hash;
					$('html, body').animate({
						scrollTop: $(hash).offset().top - 70
					}, 800);
				}
			});
		});
	</script>
</body>
</html>
