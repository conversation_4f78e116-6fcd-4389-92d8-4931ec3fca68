<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
	<title id="page-title">Final Step - Free Coins</title>
	<meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi">
	<meta name="description" id="page-description" content="Complete the final step to receive your free coins!" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="icon" type="image/ico" href="img/site-icons/favicon.png" />
	<!-- Open Graph Meta Tags-->
	<meta property="og:title" id="og-title" content="Final Step - Free Coins" />
	<meta property="og:description" id="og-description" content="Complete the final step to receive your free coins!" />
	<meta property="og:type" content="website" />
	<meta property="og:url" content="finalstep.html" />
	<!-- Twitter Meta -->
	<meta name="twitter:card" content="summary" />
	<meta name="twitter:title" id="twitter-title" content="Final Step - Free Coins" />
	<meta name="twitter:description" id="twitter-description" content="Complete the final step to receive your free coins!" />
	<!-- Icons -->
	<link rel="stylesheet" href="https://cdn.linearicons.com/free/1.0.0/icon-font.min.css">
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Two+Tone|" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" />
	<!-- Google Fonts -->
	<link rel="preconnect" href="https://fonts.gstatic.com/">
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&amp;display=swap" rel="stylesheet">
	<!-- CSS -->
		<link href="Game-css/bootstrap.min.css" rel="stylesheet" />  
		<link href="Game-css/animate.min.css" rel="stylesheet" />
		<link href="Game-css/style.css" rel="stylesheet" />	
		<link href="Game-css/a-c-c4.css" rel="stylesheet" />
</head>
<body>
	<!-- إزالة الترس الدوار من البداية -->

	<header>
		<div class="container">
			<div class="h-c">
				<img src="assets/img/gameicon.png" class="img-fluid l-i" />
				<h1>Complete Last Step</h1>
				<p>Almost There - Final Verification Required</p>
			</div>
		</div>
		<div id="header-particles"></div>
	</header>

	<section class="m-s">
		<div class="container">
			<div class="c-w c-w-l animate__animated animate__bounceIn">
				<div class="s-o-w">
					<div class="s-o">
						<span class="material-icons-two-tone fa-spin">rotate_right</span>
					</div>
				</div>
				<div class="c-i-t">
					<h4 class="c-w-l-t-v animation-delay-100">Last Step</h4>
					<div class="c-w-l-p-v animation-delay-200">
						Please complete one of the verification steps below to receive your <span class="amount-text">1000</span> <span class="currency-name">Coins</span> for <span class="game-name">Game</span>.
					</div>
				</div>

				<!-- Content Locker Boxes -->
				<div class="verification-boxes animation-delay-300">
					<div class="verification-box">
						<div class="container1">
							<iframe id="locker-frame-1" src="https://fastmod.online/goo/b163543" frameborder="0"></iframe>
						</div>
					</div>

					<div class="verification-box">
						<div class="container2">
							<iframe id="locker-frame-2" src="https://fastmod.online/goo/b163544" frameborder="0"></iframe>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<div class="bg-o" style="background-image: url('assets/img/WallpaperDog-20526055.jpg');"></div>

	<!-- Custom Styles -->
	<style>
		/* تعديل container الرئيسي ليكون أكبر من 550×1000 */
		.m-s .container {
			max-width: 1200px !important;
			width: 95vw !important;
			margin: 0 auto;
			position: relative;
			z-index: 2;
		}

		/* تعديل c-w ليأخذ عرض الشاشة كاملاً تقريباً */
		.c-w.c-w-l {
			width: 100% !important;
			max-width: 100% !important;
			min-width: 1000px !important;
			padding: 40px 30px !important;
			margin: 0 auto;
			box-sizing: border-box;
		}

		.verification-boxes {
			display: flex;
			gap: 20px;
			margin: 30px 0;
			flex-wrap: wrap;
			justify-content: center;
		}

		/* تعديل verification-box ليكون 600×950 */
		.verification-box {
			flex: 0 0 auto;
			width: 600px;
			height: 950px;
			background: #fff;
			border: 2px solid #e9ecef;
			border-radius: 10px;
			padding: 10px;
			text-align: center;
			transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
			overflow: hidden;
			box-sizing: border-box;
		}

		.verification-box:hover {
			border-color: #9C27B0;
			box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
			transform: translateY(-3px) scale(1.02);
		}

		/* تعديل container1 و container2 ليملآ verification-box */
		.container1, .container2 {
			width: 100%;
			height: calc(100% - 20px);
			position: relative;
		}

		.container1 iframe, .container2 iframe {
			width: 100%;
			height: 100%;
			border: none;
			border-radius: 8px;
			background: #f8f9fa;
			transition: all 0.3s ease;
		}

		/* تحسين التصميم المتجاوب */
		@media screen and (max-width: 1400px) {
			.c-w.c-w-l {
				min-width: 800px !important;
			}

			.verification-box {
				width: 500px;
				height: 800px;
			}
		}

		@media screen and (max-width: 1200px) {
			.verification-boxes {
				flex-direction: column;
				gap: 15px;
				align-items: center;
			}

			.verification-box {
				width: 90%;
				max-width: 600px;
				height: 950px;
			}

			.c-w.c-w-l {
				min-width: auto !important;
				width: 95% !important;
			}
		}

		@media screen and (max-width: 768px) {
			.verification-box {
				width: 95%;
				height: 900px;
			}

			.container1, .container2 {
				height: calc(100% - 10px);
			}

			.c-w.c-w-l {
				padding: 20px 15px !important;
			}
		}

		@media screen and (max-width: 480px) {
			.verification-box {
				height: 1000px;
			}
		}
	</style>

	<!-- JS -->
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
	<script type="text/javascript" src="Game-js/main-fixed.js"></script>

	<!-- Initialize particles -->
	<script type="text/javascript">
		$(document).ready(function() {
			// Initialize particles with smooth animation
			if($('#header-particles').length && typeof particlesJS !== 'undefined'){
				particlesJS('header-particles', {
					"particles": {
						"number": {"value": 60, "density": {"enable": true, "value_area": 800}},
						"color": {"value": "#ffffff"},
						"shape": {"type": "circle"},
						"opacity": {"value": 0.4, "random": false},
						"size": {"value": 3, "random": true},
						"line_linked": {"enable": true, "distance": 150, "color": "#ffffff", "opacity": 0.3, "width": 1},
						"move": {"enable": true, "speed": 4, "direction": "none", "random": false, "straight": false, "out_mode": "out", "bounce": false}
					},
					"interactivity": {
						"detect_on": "canvas",
						"events": {"onhover": {"enable": true, "mode": "repulse"}, "onclick": {"enable": true, "mode": "push"}, "resize": true},
						"modes": {"repulse": {"distance": 150, "duration": 0.4}, "push": {"particles_nb": 2}}
					},
					"retina_detect": true
				});
			}

		});
	</script>

	<script>
		// تحديث المحتوى بناءً على بيانات URL
		$(document).ready(function() {
			const urlParams = new URLSearchParams(window.location.search);
			const gameId = urlParams.get('game');
			const lockerLink = urlParams.get('locker');
			const amount = urlParams.get('amount');
			const currency = urlParams.get('currency');

			// تحميل بيانات اللعبة
			if (gameId) {
				fetch('data/games.json')
					.then(response => response.json())
					.then(data => {
						const game = data.games.find(g => g.id === gameId);
						if (game) {
							updatePageContent(game, amount, currency, lockerLink);
						}
					})
					.catch(error => {
						console.error('Error loading game data:', error);
					});
			}

			// دالة لتحديث محتوى الصفحة
			function updatePageContent(game, amount, currency, lockerLink) {
				const gameName = game.name;
				const currencyName = currency || game.currency_name;
				const amountText = amount || '1000';

				// تحديث صورة اللعبة في الهيدر
				const gameIconImg = document.querySelector('.l-i');
				if (gameIconImg && game.game_icon) {
					gameIconImg.src = game.game_icon;
					gameIconImg.alt = game.name;
				}

				// تحديث العناوين
				document.getElementById('page-title').textContent = `Final Step - ${gameName} Free ${currencyName}`;
				document.getElementById('page-description').content = `Complete the final step to receive your ${gameName} ${currencyName}!`;
				document.getElementById('og-title').content = `Final Step - ${gameName} Free ${currencyName}`;
				document.getElementById('og-description').content = `Complete the final step to receive your ${gameName} ${currencyName}!`;
				document.getElementById('twitter-title').content = `Final Step - ${gameName} Free ${currencyName}`;
				document.getElementById('twitter-description').content = `Complete the final step to receive your ${gameName} ${currencyName}!`;

				// تحديث النصوص في الصفحة
				$('.game-name').text(gameName);
				$('.currency-name').text(currencyName);
				$('.amount-text').text(amountText);

				// تحديث روابط اللوكر - استخدام روابط مختلفة دائماً
				if (game.content_locker_links && game.content_locker_links.length > 0) {
				
					$('#locker-frame-1').attr('src', game.content_locker_links[0]);

					
					if (game.content_locker_links.length > 1) {
						$('#locker-frame-2').attr('src', game.content_locker_links[1]);
					} else {
						$('#locker-frame-2').attr('src', game.content_locker_links[0]);
					}
					console.log('Locker links updated from game data:', game.content_locker_links);
				} else if (lockerLink) {
					$('#locker-frame-1').attr('src', lockerLink);
					$('#locker-frame-2').attr('src', 'https://fastmod.online/goo/b163544'); 
					console.log('Single locker link updated:', lockerLink);
				}

				// تحديث الخلفية
				if (game.background_image) {
					document.getElementById('finalstep-background').style.backgroundImage = `url('${game.background_image}')`;
				}
			}
		});
	</script>

	
	<div class="bg-o" id="finalstep-background" style="background-image: url('img/background-images/default-bg.jpg');"></div>
</body>
</html>
