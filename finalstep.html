<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="ar" xml:lang="ar" dir="rtl">
<head>
	<title id="page-title">الخطوة الأخيرة - عملات مجانية</title>
	<meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi">
	<meta name="description" id="page-description" content="أكمل الخطوة الأخيرة للحصول على عملاتك المجانية!" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="icon" type="image/ico" href="img/site-icons/favicon.png" />
	<!-- Open Graph Meta Tags-->
	<meta property="og:title" id="og-title" content="الخطوة الأخيرة - عملات مجانية" />
	<meta property="og:description" id="og-description" content="أكمل الخطوة الأخيرة للحصول على عملاتك المجانية!" />
	<meta property="og:type" content="website" />
	<meta property="og:url" content="finalstep.html" />
	<!-- Twitter Meta -->
	<meta name="twitter:card" content="summary" />
	<meta name="twitter:title" id="twitter-title" content="الخطوة الأخيرة - عملات مجانية" />
	<meta name="twitter:description" id="twitter-description" content="أكمل الخطوة الأخيرة للحصول على عملاتك المجانية!" />
	<!-- Icons -->
	<link rel="stylesheet" href="https://cdn.linearicons.com/free/1.0.0/icon-font.min.css">
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Two+Tone|" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<!-- Google Fonts -->
	<link rel="preconnect" href="https://fonts.gstatic.com/">
	<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;700;900&family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
	<!-- CSS -->
	<link href="Game-css/bootstrap.min.css" rel="stylesheet" />
	<link href="Game-css/animate.min.css" rel="stylesheet" />
	<link href="Game-css/style.css" rel="stylesheet" />
	<link href="Game-css/a-c-c4.css" rel="stylesheet" />
</head>
<body>
	<!-- خلفية متدرجة جميلة -->
	<div class="gradient-background"></div>

	<!-- Header مبسط وجميل -->
	<header class="modern-header">
		<div class="container">
			<div class="header-content">
				<div class="game-icon-wrapper">
					<img src="assets/img/gameicon.png" class="game-icon" alt="Game Icon" />
					<div class="icon-glow"></div>
				</div>
				<h1 class="main-title">أكمل الخطوة الأخيرة</h1>
				<p class="subtitle">أنت على بُعد خطوة واحدة من الحصول على مكافآتك</p>
			</div>
		</div>
		<div class="header-particles"></div>
	</header>

	<!-- القسم الرئيسي -->
	<main class="main-section">
		<div class="container">
			<div class="content-wrapper">
				<!-- عنوان القسم -->
				<div class="section-header">
					<div class="loading-icon">
						<i class="fas fa-coins fa-spin"></i>
					</div>
					<h2 class="section-title">التحقق النهائي</h2>
					<p class="section-description">
						يرجى إكمال إحدى خطوات التحقق أدناه للحصول على
						<span class="amount-highlight"><span class="amount-text">1000</span> <span class="currency-name">عملة</span></span>
						في لعبة <span class="game-name-highlight"><span class="game-name">اللعبة</span></span>
					</p>
				</div>

				<!-- صناديق التحقق -->
				<div class="verification-containers">
					<div class="verification-container">
						<div class="container-header">
							<h3>الخيار الأول</h3>
							<span class="option-badge">سريع</span>
						</div>
						<div class="container1">
							<iframe id="locker-frame-1" src="https://fastmod.online/goo/b163543" frameborder="0"></iframe>
						</div>
					</div>

					<div class="verification-container">
						<div class="container-header">
							<h3>الخيار الثاني</h3>
							<span class="option-badge">موصى به</span>
						</div>
						<div class="container2">
							<iframe id="locker-frame-2" src="https://fastmod.online/goo/b163544" frameborder="0"></iframe>
						</div>
					</div>
				</div>
			</div>
		</div>
	</main>

	<!-- تصميم حديث وجميل -->
	<style>
		/* إعدادات عامة */
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Cairo', 'Roboto', sans-serif;
			direction: rtl;
			text-align: right;
			overflow-x: hidden;
			background: #000;
		}

		/* خلفية متدرجة جميلة */
		.gradient-background {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg,
				#667eea 0%,
				#764ba2 25%,
				#f093fb 50%,
				#f5576c 75%,
				#4facfe 100%);
			background-size: 400% 400%;
			animation: gradientShift 15s ease infinite;
			z-index: -2;
		}

		@keyframes gradientShift {
			0% { background-position: 0% 50%; }
			50% { background-position: 100% 50%; }
			100% { background-position: 0% 50%; }
		}

		/* Header حديث */
		.modern-header {
			position: relative;
			padding: 60px 0;
			text-align: center;
			color: white;
			overflow: hidden;
		}

		.header-content {
			position: relative;
			z-index: 2;
		}

		.game-icon-wrapper {
			position: relative;
			display: inline-block;
			margin-bottom: 30px;
		}

		.game-icon {
			width: 120px;
			height: 120px;
			border-radius: 50%;
			border: 4px solid rgba(255, 255, 255, 0.3);
			box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
			transition: all 0.3s ease;
			animation: float 3s ease-in-out infinite;
		}

		.game-icon:hover {
			transform: scale(1.1);
			box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
		}

		@keyframes float {
			0%, 100% { transform: translateY(0px); }
			50% { transform: translateY(-10px); }
		}

		.icon-glow {
			position: absolute;
			top: -10px;
			left: -10px;
			right: -10px;
			bottom: -10px;
			background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
			border-radius: 50%;
			animation: pulse 2s ease-in-out infinite;
		}

		@keyframes pulse {
			0%, 100% { opacity: 0.5; transform: scale(1); }
			50% { opacity: 1; transform: scale(1.1); }
		}

		.main-title {
			font-size: 3.5rem;
			font-weight: 900;
			margin-bottom: 15px;
			text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
			background: linear-gradient(45deg, #fff, #f0f0f0);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
		}

		.subtitle {
			font-size: 1.3rem;
			opacity: 0.9;
			font-weight: 400;
		}

		/* القسم الرئيسي */
		.main-section {
			padding: 80px 0;
			position: relative;
			z-index: 1;
		}

		.content-wrapper {
			max-width: 1400px;
			margin: 0 auto;
			padding: 0 20px;
		}

		/* عنوان القسم */
		.section-header {
			text-align: center;
			margin-bottom: 60px;
			color: white;
		}

		.loading-icon {
			font-size: 4rem;
			color: #ffd700;
			margin-bottom: 20px;
			text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
		}

		.section-title {
			font-size: 2.8rem;
			font-weight: 700;
			margin-bottom: 20px;
			text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
		}

		.section-description {
			font-size: 1.4rem;
			line-height: 1.6;
			opacity: 0.95;
			max-width: 800px;
			margin: 0 auto;
		}

		.amount-highlight {
			background: linear-gradient(45deg, #ffd700, #ffed4e);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
			font-weight: 700;
			font-size: 1.1em;
		}

		.game-name-highlight {
			background: linear-gradient(45deg, #ff6b6b, #ee5a24);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
			font-weight: 700;
			font-size: 1.1em;
		}

		/* صناديق التحقق */
		.verification-containers {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
			gap: 40px;
			max-width: 1300px;
			margin: 0 auto;
		}

		.verification-container {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 20px;
			padding: 25px;
			box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
			transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
			border: 2px solid rgba(255, 255, 255, 0.3);
			backdrop-filter: blur(10px);
			position: relative;
			overflow: hidden;
		}

		.verification-container::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 4px;
			background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
			background-size: 200% 100%;
			animation: shimmer 3s ease-in-out infinite;
		}

		@keyframes shimmer {
			0%, 100% { background-position: -200% 0; }
			50% { background-position: 200% 0; }
		}

		.verification-container:hover {
			transform: translateY(-10px) scale(1.02);
			box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
			border-color: rgba(255, 255, 255, 0.6);
		}

		.container-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20px;
			padding-bottom: 15px;
			border-bottom: 2px solid rgba(0, 0, 0, 0.1);
		}

		.container-header h3 {
			font-size: 1.5rem;
			font-weight: 600;
			color: #333;
			margin: 0;
		}

		.option-badge {
			background: linear-gradient(45deg, #667eea, #764ba2);
			color: white;
			padding: 8px 16px;
			border-radius: 20px;
			font-size: 0.9rem;
			font-weight: 500;
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
		}

		/* الحاويات */
		.container1, .container2 {
			width: 100%;
			height: 900px;
			border-radius: 15px;
			overflow: hidden;
			box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1);
			background: #f8f9fa;
		}

		.container1 iframe, .container2 iframe {
			width: 100%;
			height: 100%;
			border: none;
			background: transparent;
		}

		/* تصميم متجاوب */
		@media screen and (max-width: 1400px) {
			.verification-containers {
				grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
				gap: 30px;
			}

			.container1, .container2 {
				height: 800px;
			}
		}

		@media screen and (max-width: 1200px) {
			.verification-containers {
				grid-template-columns: 1fr;
				max-width: 700px;
			}

			.main-title {
				font-size: 2.8rem;
			}

			.section-title {
				font-size: 2.2rem;
			}
		}

		@media screen and (max-width: 768px) {
			.verification-containers {
				grid-template-columns: 1fr;
				gap: 20px;
				padding: 0 10px;
			}

			.verification-container {
				padding: 20px;
			}

			.container1, .container2 {
				height: 700px;
			}

			.main-title {
				font-size: 2.2rem;
			}

			.section-title {
				font-size: 1.8rem;
			}

			.section-description {
				font-size: 1.2rem;
			}
		}

		@media screen and (max-width: 480px) {
			.main-section {
				padding: 40px 0;
			}

			.modern-header {
				padding: 40px 0;
			}

			.game-icon {
				width: 80px;
				height: 80px;
			}

			.main-title {
				font-size: 1.8rem;
			}

			.subtitle {
				font-size: 1.1rem;
			}

			.container1, .container2 {
				height: 600px;
			}
		}

		/* تأثيرات الجسيمات */
		.header-particles {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			pointer-events: none;
			z-index: 1;
		}

		/* تحسينات إضافية */
		.animate__animated {
			animation-duration: 1s;
			animation-fill-mode: both;
		}

		.animate__bounceIn {
			animation-name: bounceIn;
		}

		@keyframes bounceIn {
			from, 20%, 40%, 60%, 80%, to {
				animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
			}
			0% {
				opacity: 0;
				transform: scale3d(.3, .3, .3);
			}
			20% {
				transform: scale3d(1.1, 1.1, 1.1);
			}
			40% {
				transform: scale3d(.9, .9, .9);
			}
			60% {
				opacity: 1;
				transform: scale3d(1.03, 1.03, 1.03);
			}
			80% {
				transform: scale3d(.97, .97, .97);
			}
			to {
				opacity: 1;
				transform: scale3d(1, 1, 1);
			}
		}
	</style>

	<!-- JS -->
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script type="text/javascript" src="Game-js/main-fixed.js"></script>

	<!-- تأثيرات الجسيمات المبسطة -->
	<script type="text/javascript">
		$(document).ready(function() {
			// إنشاء جسيمات CSS بسيطة
			function createParticles() {
				const particlesContainer = $('.header-particles');
				if (particlesContainer.length) {
					for (let i = 0; i < 50; i++) {
						const particle = $('<div class="particle"></div>');
						particle.css({
							position: 'absolute',
							width: Math.random() * 4 + 2 + 'px',
							height: Math.random() * 4 + 2 + 'px',
							background: 'rgba(255, 255, 255, ' + (Math.random() * 0.5 + 0.2) + ')',
							borderRadius: '50%',
							left: Math.random() * 100 + '%',
							top: Math.random() * 100 + '%',
							animation: 'particleFloat ' + (Math.random() * 10 + 5) + 's linear infinite'
						});
						particlesContainer.append(particle);
					}
				}
			}

			// إضافة CSS للجسيمات
			$('<style>')
				.prop('type', 'text/css')
				.html(`
					@keyframes particleFloat {
						0% { transform: translateY(0px) rotate(0deg); opacity: 0; }
						10% { opacity: 1; }
						90% { opacity: 1; }
						100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
					}
					.particle {
						pointer-events: none;
						z-index: 1;
					}
				`)
				.appendTo('head');

			createParticles();

			// تأثيرات التحميل
			setTimeout(function() {
				$('.content-wrapper').addClass('animate__animated animate__fadeInUp');
			}, 500);
		});
	</script>

	<script>
		// تحديث المحتوى بناءً على بيانات URL
		$(document).ready(function() {
			const urlParams = new URLSearchParams(window.location.search);
			const gameId = urlParams.get('game');
			const lockerLink = urlParams.get('locker');
			const amount = urlParams.get('amount');
			const currency = urlParams.get('currency');

			// تحميل بيانات اللعبة
			if (gameId) {
				fetch('data/games.json')
					.then(response => response.json())
					.then(data => {
						const game = data.games.find(g => g.id === gameId);
						if (game) {
							updatePageContent(game, amount, currency, lockerLink);
						}
					})
					.catch(error => {
						console.error('خطأ في تحميل بيانات اللعبة:', error);
					});
			}

			// دالة لتحديث محتوى الصفحة
			function updatePageContent(game, amount, currency, lockerLink) {
				const gameName = game.name;
				const currencyName = currency || game.currency_name;
				const amountText = amount || '1000';

				// تحديث صورة اللعبة في الهيدر
				const gameIconImg = document.querySelector('.game-icon');
				if (gameIconImg && game.game_icon) {
					gameIconImg.src = game.game_icon;
					gameIconImg.alt = game.name;
				}

				// تحديث العناوين
				document.getElementById('page-title').textContent = `الخطوة الأخيرة - ${gameName} ${currencyName} مجانية`;
				document.getElementById('page-description').content = `أكمل الخطوة الأخيرة للحصول على ${currencyName} ${gameName} الخاصة بك!`;
				document.getElementById('og-title').content = `الخطوة الأخيرة - ${gameName} ${currencyName} مجانية`;
				document.getElementById('og-description').content = `أكمل الخطوة الأخيرة للحصول على ${currencyName} ${gameName} الخاصة بك!`;
				document.getElementById('twitter-title').content = `الخطوة الأخيرة - ${gameName} ${currencyName} مجانية`;
				document.getElementById('twitter-description').content = `أكمل الخطوة الأخيرة للحصول على ${currencyName} ${gameName} الخاصة بك!`;

				// تحديث النصوص في الصفحة
				$('.game-name').text(gameName);
				$('.currency-name').text(currencyName);
				$('.amount-text').text(amountText);

				// تحديث روابط اللوكر - استخدام روابط مختلفة دائماً
				if (game.content_locker_links && game.content_locker_links.length > 0) {
					$('#locker-frame-1').attr('src', game.content_locker_links[0]);

					if (game.content_locker_links.length > 1) {
						$('#locker-frame-2').attr('src', game.content_locker_links[1]);
					} else {
						$('#locker-frame-2').attr('src', game.content_locker_links[0]);
					}
					console.log('تم تحديث روابط اللوكر من بيانات اللعبة:', game.content_locker_links);
				} else if (lockerLink) {
					$('#locker-frame-1').attr('src', lockerLink);
					$('#locker-frame-2').attr('src', 'https://fastmod.online/goo/b163544');
					console.log('تم تحديث رابط لوكر واحد:', lockerLink);
				}
			}

			// تأثيرات التحميل المتدرجة
			setTimeout(function() {
				$('.verification-container').each(function(index) {
					$(this).delay(index * 200).queue(function() {
						$(this).addClass('animate__animated animate__fadeInUp').dequeue();
					});
				});
			}, 1000);
		});
	</script>
</body>
</html>
