tik<PERSON>ok
{
  "username": "filora_gift_idea",
  "nickname": "<PERSON><PERSON>",
  "user_id": "7348201824811385889",
  "profile_image": "https://p16-sign-va.tiktokcdn.com/tos-maliva-avt-0068/c16ccc0042a73cd5e17cfc4f28146c31~tplv-tiktokx-cropcenter:1080:1080.jpeg?dr=14579&refresh_token=e9f2791b&x-expires=1752926400&x-signature=n8CXpvsOlHC713SVCZwsNDml%2Bs4%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=81f88b70&idc=maliva",
  "following": 6,
  "followers": 2,
  "total_videos": 1,
  "total_heart": 84,
  "verified": false,
  "description": "🎁 Filora gift ideas for every occasion! Explore and find the perfect one!",
  "region": "MA",
  "bio_link": {
    "link": "linktr.ee/FiloraPro",
    "risk": 0
  },
  "secondary_id": "MS4wLjABAAAAyPfSr_4CzmexxfrUKNB42RCU4rPEt8HdZYDWquy7yaghIzg7VG4F5QF8nVN-fS46",
  "is_private": false
}

instagram
{
  "pk": ***********,
  "username": "prvvv__is1",
  "full_name": "PRVVV__ISLAM",
  "is_private": false,
  "profile_pic_url": "https://scontent-cdg4-2.cdninstagram.com/v/t51.2885-19/518985087_17869603077403297_3382897499169056113_n.jpg?stp=dst-jpg_e0_s150x150_tt6&_nc_ht=scontent-cdg4-2.cdninstagram.com&_nc_cat=101&_nc_oc=Q6cZ2QFvjUXSy1j8_foYLCuV2wgkDHoR0haa37iqXWX7QLhX9Gk32-RPyg-yZzqAvjQqX3Y&_nc_ohc=2RWbj2FO2ccQ7kNvwHr9J8Q&_nc_gid=05tFyD7ECacvXWVZYIaySg&edm=AKralEIBAAAA&ccb=7-5&oh=00_AfQgW9GOBw5nRK21hx1U4ttRQ7r-tiVcJ6XfP_W_jLNuHw&oe=687EB2BD&_nc_sid=2fe71f",
  "profile_pic_id": "3675351086599836685_***********",
  "is_verified": false,
  "media_count": 6,
  "follower_count": 1238,
  "following_count": 1695,
  "total_clips_count": 1,
  "has_highlight_reels": true,
  "category": "Reel creator",
  "biography": "📍: marooc__ _ _:Fés🗿✌🏼\nMe:🤴islam|إسلام@tweets__islam__h7🧠👁\n🗣:2kmomkin📊✏🎬\n 😺:reels🖖💸🗞💰📽 al3alamiya🌍🏖\nmarka :#lacos🐊😻",
  "hd_profile_pic_url_info": {
    "width": 1080,
    "height": 1080,
    "url": "https://scontent-cdg4-2.cdninstagram.com/v/t51.2885-19/518985087_17869603077403297_3382897499169056113_n.jpg?_nc_ht=scontent-cdg4-2.cdninstagram.com&_nc_cat=101&_nc_oc=Q6cZ2QFvjUXSy1j8_foYLCuV2wgkDHoR0haa37iqXWX7QLhX9Gk32-RPyg-yZzqAvjQqX3Y&_nc_ohc=2RWbj2FO2ccQ7kNvwHr9J8Q&_nc_gid=05tFyD7ECacvXWVZYIaySg&edm=AKralEIBAAAA&ccb=7-5&oh=00_AfQNQi4MMmWUd5rkjVeImkn9LJMQCiFFw5aZN4sykdzVsw&oe=687EB2BD&_nc_sid=2fe71f"
  },
  "account_type": 3,
  "fan_club_info": {},
  "can_hide_category": true,
  "fbid_v2": *****************,
  "pk_id": "***********",
  "spam_follower_setting_enabled": true,
  "strong_id__": "***********",
  "biography_with_entities": {
    "raw_text": "📍: marooc__ _ _:Fés🗿✌🏼\nMe:🤴islam|إسلام@tweets__islam__h7🧠👁\n🗣:2kmomkin📊✏🎬\n 😺:reels🖖💸🗞💰📽 al3alamiya🌍🏖\nmarka :#lacos🐊😻",
    "entities": [
      {
        "hashtag": {
          "id": "*****************",
          "name": "lacos🐊😻"
        }
      }
    ]
  },
  "can_hide_public_contacts": true,
  "should_show_category": true,
  "category_id": ****************,
  "is_category_tappable": true,
  "is_eligible_for_smb_support_flow": true,
  "lead_details_app_id": "com.bloks.www.ig.smb.services.lead_gen.all_leads",
  "professional_conversion_suggested_account_type": 2,
  "business_contact_method": "UNKNOWN",
  "page_id": ***************,
  "page_name": "IG Ads Identity",
  "active_standalone_fundraisers": {},
  "has_chaining": true,
  "hd_profile_pic_versions": [
    {
      "width": 320,
      "height": 320,
      "url": "https://scontent-cdg4-2.cdninstagram.com/v/t51.2885-19/518985087_17869603077403297_3382897499169056113_n.jpg?stp=dst-jpg_s320x320_tt6&_nc_ht=scontent-cdg4-2.cdninstagram.com&_nc_cat=101&_nc_oc=Q6cZ2QFvjUXSy1j8_foYLCuV2wgkDHoR0haa37iqXWX7QLhX9Gk32-RPyg-yZzqAvjQqX3Y&_nc_ohc=2RWbj2FO2ccQ7kNvwHr9J8Q&_nc_gid=05tFyD7ECacvXWVZYIaySg&edm=AKralEIBAAAA&ccb=7-5&oh=00_AfQMfMqXSoKVIzf1j2Yu_tGqjzoGmc16nLR9nn3wmh1Bhg&oe=687EB2BD&_nc_sid=2fe71f"
    },
    {
      "width": 640,
      "height": 640,
      "url": "https://scontent-cdg4-2.cdninstagram.com/v/t51.2885-19/518985087_17869603077403297_3382897499169056113_n.jpg?stp=dst-jpg_s640x640_tt6&_nc_ht=scontent-cdg4-2.cdninstagram.com&_nc_cat=101&_nc_oc=Q6cZ2QFvjUXSy1j8_foYLCuV2wgkDHoR0haa37iqXWX7QLhX9Gk32-RPyg-yZzqAvjQqX3Y&_nc_ohc=2RWbj2FO2ccQ7kNvwHr9J8Q&_nc_gid=05tFyD7ECacvXWVZYIaySg&edm=AKralEIBAAAA&ccb=7-5&oh=00_AfQ0z3aX8KgJiSq30y5iZGu5UfFm3A4XomL1WoRw8cAtCA&oe=687EB2BD&_nc_sid=2fe71f"
    }
  ],
  "live_subscription_status": "default",
  "pinned_channels_info": {},
  "auto_expand_chaining": false,
  "avatar_status": {
    "has_avatar": false
  },
  "existing_user_age_collection_enabled": true,
  "has_public_tab_threads": true,
  "include_direct_blacklist_status": true,
  "interop_messaging_user_fbid": *****************,
  "is_direct_roll_call_enabled": true,
  "is_profile_broadcast_sharing_enabled": true,
  "is_profile_picture_expansion_enabled": true,
  "is_remix_setting_enabled_for_posts": true,
  "is_remix_setting_enabled_for_reels": true,
  "is_secondary_account_creation": true,
  "nametag": {
    "available_theme_colors": [
      -1,
      7747834,
      ********,
      ********,
      ********,
      0
    ],
    "background_image_url": "",
    "emoji": "😀",
    "emoji_color": -********,
    "gradient": 1,
    "is_background_image_blurred": false,
    "mode": 0,
    "selected_theme_color": -1,
    "selfie_sticker": 0,
    "selfie_url": "",
    "theme_color": {
      "available_theme_colors": [
        {
          "display_label": "Default",
          "int_value": -1
        },
        {
          "display_label": "Purple",
          "int_value": 7747834
        },
        {
          "display_label": "Lavender",
          "int_value": ********
        },
        {
          "display_label": "Pink",
          "int_value": ********
        },
        {
          "display_label": "Orange",
          "int_value": ********
        },
        {
          "display_label": "Black",
          "int_value": 0
        }
      ],
      "selected_theme_color": {
        "display_label": "Default",
        "int_value": -1
      }
    }
  },
  "open_external_url_with_in_app_browser": true,
  "recs_from_friends": {
    "recs_from_friends_entry_point_type": "banner"
  },
  "show_account_transparency_details": true,
  "show_post_insights_entry_point": true,
  "seller_shoppable_feed_type": "none",
  "merchant_checkout_style": "none",
  "creator_shopping_info": {}
}
snapchat
{
  "error": {
    "isError": false,
    "errorMsg": null
  },
  "accountType": "unknown",
  "name": "Amal Ino😍",
  "snapcodeURL": "https://app.snapchat.com/web/deeplink/snapcode?username=amalghannoui&type=SVG",
  "privateAccountData": {
    "bitmojiURL": null
  },
  "publicAccountData": {
    "address": null,
    "bio": null,
    "profilePictureURL": null,
    "subscriberCount": null,
    "websiteURL": null,
    "spotlightHightlightSnaps": [],
    "curatedHighlightSnaps": [],
    "latestStorySnaps": []
  }
}