// DOM Elements
const startBoostBtn = document.getElementById('startBoostBtn');
const platformsBtn = document.getElementById('platformsBtn');
const mockFollowers = document.getElementById('mockFollowers');
const statNumbers = document.querySelectorAll('.stat-number');
const platformCards = document.querySelectorAll('.platform-card');
const platformBtns = document.querySelectorAll('.platform-btn');

// Navigation functionality
const navToggle = document.querySelector('.nav-toggle');
const navMenu = document.querySelector('.nav-menu');

navToggle?.addEventListener('click', () => {
    navMenu.classList.toggle('active');
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Counter animation for stats
function animateCounter(element, target, duration = 2000) {
    const start = 0;
    const increment = target / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        // Format number with commas
        if (target >= 1000000) {
            element.textContent = (current / 1000000).toFixed(1) + 'M';
        } else if (target >= 1000) {
            element.textContent = (current / 1000).toFixed(0) + 'K';
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }
    }, 16);
}

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate');
            
            // Animate counters when they come into view
            if (entry.target.classList.contains('stat-number')) {
                const target = parseInt(entry.target.getAttribute('data-target'));
                animateCounter(entry.target, target);
            }
        }
    });
}, observerOptions);

// Observe elements for animation
statNumbers.forEach(stat => observer.observe(stat));

// Mock follower count animation
let followerCount = 1234;
function animateMockFollowers() {
    setInterval(() => {
        followerCount += Math.floor(Math.random() * 5) + 1;
        if (mockFollowers) {
            mockFollowers.textContent = followerCount.toLocaleString();
        }
    }, 2000);
}

// Start animations when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Animate hero elements
    setTimeout(() => {
        document.querySelector('.hero-title')?.classList.add('animate');
    }, 300);
    
    setTimeout(() => {
        document.querySelector('.hero-description')?.classList.add('animate');
    }, 600);
    
    setTimeout(() => {
        document.querySelector('.hero-buttons')?.classList.add('animate');
    }, 900);
    
    // Start mock follower animation
    animateMockFollowers();
    
    // Add scroll effect to navbar
    window.addEventListener('scroll', () => {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
});

// Button click handlers
startBoostBtn?.addEventListener('click', () => {
    // Redirect to Instagram by default
    window.location.href = 'Instagram/Instagram.html';
});

platformsBtn?.addEventListener('click', () => {
    // Scroll to platforms section
    document.getElementById('platforms')?.scrollIntoView({
        behavior: 'smooth'
    });
});

// Platform card click handlers
platformCards.forEach(card => {
    card.addEventListener('click', () => {
        const platform = card.getAttribute('data-platform');
        // Store selected platform and redirect to platform folder
        localStorage.setItem('selectedPlatform', platform);

        // Redirect to specific platform folder
        if (platform === 'instagram') {
            window.location.href = 'Instagram/Instagram.html';
        } else if (platform === 'tiktok') {
            window.location.href = 'TikTok/TikTok.html';
        } else if (platform === 'snapchat') {
            window.location.href = 'Snapchat/Snapchat.html';
        } else {
            // Default to Instagram
            window.location.href = 'Instagram/Instagram.html';
        }
    });
});

// Platform button click handlers
platformBtns.forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent card click
        const card = btn.closest('.platform-card');
        const platform = card.getAttribute('data-platform');
        // Store selected platform and redirect to platform folder
        localStorage.setItem('selectedPlatform', platform);

        // Redirect to specific platform folder
        if (platform === 'instagram') {
            window.location.href = 'Instagram/Instagram.html';
        } else if (platform === 'tiktok') {
            window.location.href = 'TikTok/TikTok.html';
        } else if (platform === 'snapchat') {
            window.location.href = 'Snapchat/Snapchat.html';
        } else {
            // Default to Instagram
            window.location.href = 'Instagram/Instagram.html';
        }
    });
});

// Add click handlers for pricing buttons
document.querySelectorAll('.pricing-card .btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.preventDefault();
        // Add ripple effect
        const ripple = document.createElement('span');
        ripple.classList.add('ripple');
        btn.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
        
        // Redirect to booster page
        setTimeout(() => {
            window.location.href = 'booster.html';
        }, 300);
    });
});

// Parallax effect for hero background
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.hero-bg-animation');
    if (parallax) {
        const speed = scrolled * 0.5;
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Add hover effects to feature cards
document.querySelectorAll('.feature-card').forEach(card => {
    card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-10px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0) scale(1)';
    });
});

// Add floating animation to pricing cards
document.querySelectorAll('.pricing-card').forEach((card, index) => {
    card.style.animationDelay = `${index * 0.2}s`;
    card.classList.add('float-in');
});

// Typing effect for hero title
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    .animate {
        animation: fadeInUp 0.8s ease forwards;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .float-in {
        animation: floatIn 0.8s ease forwards;
    }
    
    @keyframes floatIn {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: rippleEffect 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes rippleEffect {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .navbar.scrolled {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }
    
    @media (max-width: 768px) {
        .nav-menu.active {
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            gap: 15px;
        }
    }
`;

document.head.appendChild(style);

// Add loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
});

// Performance optimization: Lazy load images
const images = document.querySelectorAll('img[data-src]');
const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
        }
    });
});

images.forEach(img => imageObserver.observe(img));
